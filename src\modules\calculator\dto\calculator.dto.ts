import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TradingCalculatorDto {
  @ApiProperty({
    description: 'Unit type for calculation',
    enum: ['pips', 'usd'],
    example: 'pips'
  })
  @IsString()
  @IsIn(['pips', 'usd'])
  unit: 'pips' | 'usd';

  @ApiProperty({
    description: 'Lot size',
    enum: [1, 0.1, 0.01],
    example: 1
  })
  @IsNumber()
  @IsIn([1, 0.1, 0.01])
  lots: number;

  @ApiProperty({
    description: 'Number of trades per day',
    minimum: 1,
    maximum: 50,
    example: 5
  })
  @IsNumber()
  @Min(1)
  @Max(50)
  tradesPerDay: number;

  @ApiProperty({
    description: 'Rebate amount',
    minimum: 0.10,
    maximum: 1.30,
    example: 0.50
  })
  @IsNumber()
  @Min(0.10)
  @Max(10)
  rebate: number;
}