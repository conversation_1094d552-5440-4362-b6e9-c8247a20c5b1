import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class ReferralProfits extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId; // Jo user ko referral profit mila hai

  @Prop({ required: true, ref: 'Auth' })
  referredUserId: MongooseSchema.Types.ObjectId; // Jis user ne trade kiya hai

  @Prop({ required: true, ref: 'Broker' })
  brokerId: MongooseSchema.Types.ObjectId; // Kis broker se profit aya hai

  @Prop({ required: true })
  referralLevel: number; // 1, 2, or 3

  @Prop({ required: true })
  profitAmount: number; // Kitna profit mila hai

  @Prop({ required: true })
  tradingAccount: string; // Trading account jis se profit aya hai

  @Prop({ required: true })
  description: string; // Description ke liye

  @Prop({ default: Date.now })
  earnedAt: Date;
}

export const ReferralProfitsSchema = SchemaFactory.createForClass(ReferralProfits);
