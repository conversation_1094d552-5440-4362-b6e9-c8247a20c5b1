import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Notification extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  message: string;

  @Prop({ 
    required: true, 
    enum: [
      'withdrawal_success',
      'withdrawal_rejected', 
      'support',
      'referral_level1',
      'referral_level2', 
      'referral_level3',
      'new_broker',
      'rebate',
      'leaderboard',
      'review',
      'account_locked',
      'account_unlocked',
      'broker_verification_success',
      'broker_verification_denied',
      'cashback',
      'account_verified',
      'account_denied'
    ]
  })
  type: string;

  @Prop({ default: false })
  isRead: boolean;

  @Prop({ type: Object, default: {} })
  metadata: {
    withdrawalId?: string;
    supportId?: string;
    brokerId?: string;
    amount?: number;
    referralUserId?: string;
    level?: number;
    accountId?: string;
    reviewId?: string;
    cashbackAmount?: number;
  };

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const NotificationSchema = SchemaFactory.createForClass(Notification);