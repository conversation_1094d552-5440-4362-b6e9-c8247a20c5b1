import { IsString, <PERSON>E<PERSON>, <PERSON><PERSON>ption<PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSupportDto {
  @ApiProperty({
    description: 'Name of the person creating support ticket',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Category of the support ticket',
    example: 'Technical Issue',
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: 'Detailed description of the issue',
    example: 'I am facing login issues with my account',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Email address for contact',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User ID (automatically filled from JWT token)',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'title of the support',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;
}

export class AdminReplyDto {
  @IsString()
  @IsNotEmpty()
  adminReplyTitle: string;

  @IsString()
  @IsNotEmpty()
  adminReplyMessage: string;
}
