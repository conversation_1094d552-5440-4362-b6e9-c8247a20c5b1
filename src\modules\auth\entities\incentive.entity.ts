import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Incentive extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  userName: string;

  @Prop({ required: true })
  cashback: number;

  @Prop({ required: true })
  rank: number;

  @Prop({ default: 'active', enum: ['active', 'cancelled'] })
  status: string;

  @Prop({ default: '' })
  description: string;

  @Prop({ default: Date.now })
  givenAt: Date;
}

export const IncentiveSchema = SchemaFactory.createForClass(Incentive);
