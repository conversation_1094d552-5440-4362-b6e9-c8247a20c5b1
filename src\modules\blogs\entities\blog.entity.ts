import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true }) // timestamps: true is used to add createdAt and updatedAt fields to the schema
export class Blog extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  image: string;

  @Prop({ required: true })
  mainImage: string;

  @Prop({ required: false })
  thumbnailImage: string;

  @Prop({ required: false })
  ctaTitle: string;

  @Prop({ required: false })
  subtext: string;

  @Prop({ required: false })
  ctaImage: string;

  @Prop({ required: false })
  CTAButtonText: string;

  @Prop({ required: false })
  link: string;

  @Prop({ default: 0 })
  blogClicks: number;
}

export const BlogSchema = SchemaFactory.createForClass(Blog);
