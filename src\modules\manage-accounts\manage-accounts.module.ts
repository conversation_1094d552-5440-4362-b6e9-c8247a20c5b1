import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ManageAccountsService } from './manage-accounts.service';
import { ManageAccountsController } from './manage-accounts.controller';
import { ManageAccount, ManageAccountSchema } from './entities/manage-account.entity';
import { ExchangeAccount, ExchangeAccountSchema } from './entities/exchange-account.entity';
import { Broker, BrokerSchema } from '../broker/entity/broker';
import { Exchange, ExchangeSchema } from '../exchanges/entities/exchange.entity';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ManageAccount.name, schema: ManageAccountSchema },
      { name: ExchangeAccount.name, schema: ExchangeAccountSchema },
      { name: Broker.name, schema: BrokerSchema },
      { name: Exchange.name, schema: ExchangeSchema },
      { name: Auth.name, schema: AuthSchema }
    ])
  ],
  controllers: [ManageAccountsController],
  providers: [ManageAccountsService],
  exports: [ManageAccountsService]
})
export class ManageAccountsModule {}
