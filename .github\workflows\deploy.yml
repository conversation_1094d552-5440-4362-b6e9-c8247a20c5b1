name: Auto Deploy to GCP VM

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: SSH and deploy to GCP
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          script: |
            source ~/.bashrc
            source ~/.nvm/nvm.sh
            cd TradeReward-Backend/
            git pull origin master
            npm install --force
            npm run build
            pm2 restart 0
