import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class CsvHistory extends Document {
  @Prop({ required: true, ref: 'Broker' })
  brokerId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  startDate: string;

  @Prop({ required: true })
  endDate: string;

  @Prop({ required: true, default: 0 })
  totalRebate: number;
}

export const CsvHistorySchema = SchemaFactory.createForClass(CsvHistory);