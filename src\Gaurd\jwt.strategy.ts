import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../modules/auth/auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    });
  }

  async validate(payload: any) {
    console.log('🔍 JWT Payload received:', payload);
    
    const user = await this.authService.findOne(payload.sub);
    if (!user) {
      console.log('❌ User not found in JWT validation');
      throw new UnauthorizedException('User not found');
    }
    
    console.log('✅ JWT validation successful for user:', user.email);
    return {
      userId: user._id,
      sub: user._id, // Add sub for compatibility
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName
    };
  }
}
