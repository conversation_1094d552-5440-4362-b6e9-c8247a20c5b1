import { IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateBlogDto {
  @ApiProperty({ description: 'Blog title', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Main Image', required: false })
  @IsString()
  @IsOptional()
  mainImage?: string;

  @ApiProperty({ description: 'CTA Button Text', required: false })
  @IsString()
  @IsOptional()
  CTAButtonText?: string;

  @ApiProperty({ description: 'Blog content', required: false })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({ description: 'Blog image URL', required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Blog thumbnail image URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailImage?: string;

  @ApiProperty({ description: 'Call to action title', required: false })
  @IsString()
  @IsOptional()
  ctaTitle?: string;

  @ApiProperty({ description: 'Blog subtext', required: false })
  @IsString()
  @IsOptional()
  subtext?: string;

  @ApiProperty({ description: 'Call to action image URL', required: false })
  @IsString()
  @IsOptional()
  ctaImage?: string;

  @ApiProperty({ description: 'Blog link URL', required: false })
  @IsString()
  @IsOptional()
  link?: string;
}
