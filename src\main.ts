import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
// import { testAirDrop } from './utils/helper';
import { LiveChatService } from './modules/chat/chat.service';
import { Server } from 'socket.io';
import { testAirDrop } from './utils/helper';
import { testSolanaTransfer } from './utils/solana-helper';
import { NotificationsService } from './modules/notifications/notifications.service';
import { testBscAirDrop } from './utils/bsc-helper';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable validation globally
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Set global prefix
  app.setGlobalPrefix('/api');

  // Enable CORS
  app.enableCors();

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Trade Reward API')
    .setDescription('Trade Reward API with JWT Authentication')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
      'access-token',
    )
    .addTag('Authentication')
    .addTag('Users')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(process.env.PORT ?? 5000);
  const server = app.getHttpServer();
  const io = new Server(server, { cors: { origin: '*' } });
  console.log('Socket.IO server is Running');
  const liveChatService = app.get(LiveChatService);
  liveChatService.setIo(io);

  // Add notifications service
  const notificationsService = app.get(NotificationsService);
  notificationsService.setIo(io);

  global.onlineUsers = new Map(); // Store connected users
  io.on('connection', (socket) => {
    console.log('New client connected: ' + socket.id);

    socket.on('register', (userID) => {
      global.onlineUsers.set(userID, socket.id);
      console.log(
        'registered user: ' + userID + ' with socket ID: ' + socket.id,
      );
    });

    socket.on('disconnect', () => {
      for (const [userID, socketID] of global.onlineUsers.entries()) {
        if (socketID === socket.id) {
          global.onlineUsers.delete(userID);
          break;
        }
      }
      console.log('Client disconnected: ' + socket.id);
    });
  });

  global.io = io;
  console.log(`Application is running on: ${await app.getUrl()} `);

  // For testing (crypto payments).
  // await testAirDrop();
  // await testSolanaTransfer();
  //  testBscAirDrop();

}
bootstrap();
