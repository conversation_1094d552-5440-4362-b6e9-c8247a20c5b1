import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class ChatRoom extends Document {
  @Prop({ required: true, unique: true })
  roomId: string;

  @Prop({ required: true, ref: 'support' })
  requesterId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [String], required: true })
  users: string[];

  @Prop({ default: false })
  isGroup: boolean;

  @Prop({ required: false })
  groupName: string;

  @Prop({ required: false })
  groupPicture: string;

  @Prop({ required: false, ref: 'Auth' })
  createdBy: MongooseSchema.Types.ObjectId;
}

export const ChatRoomSchema = SchemaFactory.createForClass(ChatRoom);
