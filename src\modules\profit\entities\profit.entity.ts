import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Profit extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, ref: 'Broker' })
  brokerId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, ref: 'Exchange' })
  exchangeId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  tradingAccount: string;

  // @Prop({ required: true })
  // accountType: string;

  // @Prop({ required: true })
  // nodeName: string;

  // @Prop({ required: true })
  // platform: string;

  // @Prop({ required: true })
  // rebateMethod: string;

  // @Prop({ required: true })
  // ibCurrency: string;

  @Prop({ required: true })
  totalRebate: number; // Original rebate amount from CSV

  @Prop({ required: true })
  userProfit: number; // 60% of total rebate

  @Prop({ required: true })
  platformProfit: number; // 40% of total rebate

  // @Prop({ type: Object, default: {} })
  // tradingData: {
  //   FOREX?: number;
  //   BULLION?: number;
  //   OIL?: number;
  //   CFD?: number;
  //   CRYPTO?: number;
  //   SHARES?: number;
  //   BTCUSD?: number;
  // };

  @Prop({ default: 'processed' })
  status: string;

  @Prop({ default: Date.now })
  processedAt: Date;

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: { type: String, required: false }
    }],
    default: []
  })
  csvData: Array<{
    title: string;
    content: string;
  }>;
}

export const ProfitSchema = SchemaFactory.createForClass(Profit);
