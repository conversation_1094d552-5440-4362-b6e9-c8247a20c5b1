import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { Blog } from './entities/blog.entity';

@Injectable()
export class BlogsService {
  constructor(@InjectModel(Blog.name) private blogModel: Model<Blog>) {}

  async create(createBlogDto: CreateBlogDto): Promise<Blog> {
    try {
      const createdBlog = new this.blogModel(createBlogDto);
      return await createdBlog.save();
    } catch (error) {
      throw new Error(`Failed to create blog: ${error.message}`);
    }
  }

  async findAll(): Promise<Blog[]> {
    try {
      return await this.blogModel.find().sort({ createdAt: -1 }).exec();
    } catch (error) {
      throw new Error(`Failed to fetch blogs: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<Blog> {
    try {
      const blog = await this.blogModel.findById(id).exec();
      if (!blog) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }
      blog.blogClicks += 1;
      await blog.save();
      return blog;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch blog: ${error.message}`);
    }
  }

  async update(id: string, updateBlogDto: UpdateBlogDto): Promise<Blog> {
    try {
      // Pehle blog find karte hain
      const existingBlog = await this.blogModel.findById(id).exec();
      if (!existingBlog) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      // Sirf provided fields ko update object mein add karte hain
      const updateFields: any = {};

      if (updateBlogDto.title !== undefined) {
        updateFields.title = updateBlogDto.title;
      }
      if (updateBlogDto.content !== undefined) {
        updateFields.content = updateBlogDto.content;
      }
      if (updateBlogDto.image !== undefined) {
        updateFields.image = updateBlogDto.image;
      }
      if (updateBlogDto.thumbnailImage !== undefined) {
        updateFields.thumbnailImage = updateBlogDto.thumbnailImage;
      }
      if (updateBlogDto.ctaTitle !== undefined) {
        updateFields.ctaTitle = updateBlogDto.ctaTitle;
      }
      if (updateBlogDto.subtext !== undefined) {
        updateFields.subtext = updateBlogDto.subtext;
      }
      if (updateBlogDto.ctaImage !== undefined) {
        updateFields.ctaImage = updateBlogDto.ctaImage;
      }
      if (updateBlogDto.link !== undefined) {
        updateFields.link = updateBlogDto.link;
      }

      // Sirf provided fields ko update karte hain
      const updatedBlog = await this.blogModel
        .findByIdAndUpdate(
          id,
          { $set: updateFields },
          { new: true, runValidators: true },
        )
        .exec();

      if (!updatedBlog) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      return updatedBlog;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to update blog: ${error.message}`);
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    try {
      const deletedBlog = await this.blogModel.findByIdAndDelete(id).exec();
      if (!deletedBlog) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }
      return { message: 'Blog deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to delete blog: ${error.message}`);
    }
  }
  async findMostBrokerBlogs(): Promise<Blog[]> {
    try {
      return await this.blogModel
        .find()
        .sort({ blogClicks: -1 })
        .limit(3)
        .exec();
    } catch (error) {
      throw new Error(`Failed to fetch blogs: ${error.message}`);
    }
  }
}
