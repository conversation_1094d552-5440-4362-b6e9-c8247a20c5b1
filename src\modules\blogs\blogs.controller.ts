import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { BlogsService } from './blogs.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { Public } from '../../Gaurd/public.decorator';

@ApiTags('Blogs')
@Controller('blogs')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BlogsController {
  constructor(private readonly blogsService: BlogsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new blog' })
  create(@Body() createBlogDto: CreateBlogDto) {
    return this.blogsService.create(createBlogDto);
  }

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get all blogs' })
  findAll() {
    return this.blogsService.findAll();
  }

  @Public()
  @Get('topBrokerBlogs')
  @ApiOperation({ summary: 'Get most clicked blogs' })
  findMostClicked() {
    return this.blogsService.findMostBrokerBlogs();
  }



  @Public()
  @Get(':id')
  @ApiOperation({ summary: 'Get blog by ID' })
  findOne(@Param('id') id: string) {
    return this.blogsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update blog by ID' })
  update(@Param('id') id: string, @Body() updateBlogDto: UpdateBlogDto) {
    return this.blogsService.update(id, updateBlogDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete blog by ID' })
  remove(@Param('id') id: string) {
    return this.blogsService.remove(id);
  }
}
