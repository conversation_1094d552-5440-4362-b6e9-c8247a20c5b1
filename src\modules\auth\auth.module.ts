import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { Auth, AuthSchema } from './entities/auth.entity';
import {
  ReferralTree,
  ReferralTreeSchema,
} from './entities/referral-tree.entity';
import { Incentive, IncentiveSchema } from './entities/incentive.entity';
import {
  CreditDebitHistory,
  CreditDebitHistorySchema,
} from '../profit/entities/credit-debit-history';
import { JwtStrategy } from '../../Gaurd/jwt.strategy';
import { MailService } from 'src/utils/sendMail';
import { NotificationsModule } from '../notifications/notifications.module';
import { TempReferal, TempReferalSchema } from './entities/tempreferral';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Auth.name, schema: AuthSchema },
      { name: ReferralTree.name, schema: ReferralTreeSchema },
      { name: Incentive.name, schema: IncentiveSchema },
      { name: CreditDebitHistory.name, schema: CreditDebitHistorySchema },
      { name: TempReferal.name, schema: TempReferalSchema },
    ]),
    PassportModule,
    JwtModule.register({
      secret:
        process.env.JWT_SECRET ||
        'your-super-secret-jwt-key-change-in-production',
      signOptions: { expiresIn: '7d' },
    }),
    NotificationsModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, MailService],
  exports: [AuthService],
})
export class AuthModule {}
