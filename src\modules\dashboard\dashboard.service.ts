import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Dashboard } from './entities/dashboard.entity';

@Injectable()
export class DashboardService {
  constructor(
    @InjectModel(Dashboard.name) private dashboardModel: Model<Dashboard>,
  ) {}

  async getDashboardStats(): Promise<any> {
    try {
      let dashboard = await this.dashboardModel.findOne();

      if (!dashboard) {
        // Create a new dashboard with UTC date
        const todayUTC = new Date(Date.UTC(
          new Date().getUTCFullYear(),
          new Date().getUTCMonth(),
          new Date().getUTCDate()
        ));
        
        dashboard = new this.dashboardModel({
          totalBrokers: 0,
          totalWithdrawals: 0,
          totalBrokerRebate: 0,
          todayBrokers: 0,
          todayWithdrawals: 0,
          todayBrokerRebate: 0,
          yesterdayBrokers: 0,
          yesterdayWithdrawals: 0,
          yesterdayBrokerRebate: 0,
          lastUpdated: todayUTC,
        });
        await dashboard.save();
      }

      // Check if we need to reset today's stats and update yesterday's stats
      await this.checkAndResetDailyStats(dashboard);

      // Calculate percentage changes
      const brokerChange = this.calculatePercentageChange(
        dashboard.todayBrokers,
        dashboard.yesterdayBrokers,
      );
      
      const withdrawalChange = this.calculatePercentageChange(
        dashboard.todayWithdrawals,
        dashboard.yesterdayWithdrawals,
      );
      
      const rebateChange = this.calculatePercentageChange(
        dashboard.todayBrokerRebate,
        dashboard.yesterdayBrokerRebate,
      );

      // Return dashboard with comparison data
      return {
        ...dashboard.toObject(),
        comparison: {
          brokers: {
            percentChange: brokerChange,
            isIncrease: brokerChange >= 0,
          },
          withdrawals: {
            percentChange: withdrawalChange,
            isIncrease: withdrawalChange >= 0,
          },
          rebates: {
            percentChange: rebateChange,
            isIncrease: rebateChange >= 0,
          },
        },
      };
    } catch (error) {
      throw new Error(`Failed to get dashboard stats: ${error.message}`);
    }
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    const change = ((current - previous) / previous) * 100;
    return Math.round(change * 100) / 100; // Round to 2 decimal places
  }

  private async checkAndResetDailyStats(dashboard: Dashboard): Promise<void> {
    // Use UTC dates for consistent time zone handling
    const today = new Date();
    const todayUTC = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()));
    
    const lastUpdated = new Date(dashboard.lastUpdated);
    const lastUpdatedUTC = new Date(Date.UTC(lastUpdated.getUTCFullYear(), lastUpdated.getUTCMonth(), lastUpdated.getUTCDate()));
    
    // Check if the last update was on a different day (using UTC)
    if (todayUTC.getTime() !== lastUpdatedUTC.getTime()) {
      // Calculate yesterday in UTC
      const yesterdayUTC = new Date(todayUTC);
      yesterdayUTC.setUTCDate(yesterdayUTC.getUTCDate() - 1);
      
      console.log("lastUpdatedUTC.getTime()", lastUpdatedUTC.getTime());
      console.log("yesterdayUTC.getTime()", yesterdayUTC.getTime());
      
      // Check if last update was yesterday (using UTC)
      const isYesterday = lastUpdatedUTC.getTime() === yesterdayUTC.getTime();
      
      if (isYesterday) {
        // If last update was yesterday, move today's values to yesterday
        console.log("is yesterday working");
        
        dashboard.yesterdayBrokers = dashboard.todayBrokers;
        dashboard.yesterdayWithdrawals = dashboard.todayWithdrawals;
        dashboard.yesterdayBrokerRebate = dashboard.todayBrokerRebate;
      } else {
        // If there was a gap (more than one day), set yesterday's values to 0
        console.log("is gap working"); 
         
        dashboard.yesterdayBrokers = 0;
        dashboard.yesterdayWithdrawals = 0;
        dashboard.yesterdayBrokerRebate = 0;
      }
      
      // Reset today's values
      dashboard.todayBrokers = 0;
      dashboard.todayWithdrawals = 0;
      dashboard.todayBrokerRebate = 0;
      
      // Update lastUpdated with current UTC date
      dashboard.lastUpdated = todayUTC;
      await dashboard.save();
    }
  }

  async incrementBrokerCount(): Promise<Dashboard> {
    try {
      let dashboard = await this.dashboardModel.findOne();

      if (!dashboard) {
        const todayUTC = new Date(Date.UTC(
          new Date().getUTCFullYear(),
          new Date().getUTCMonth(),
          new Date().getUTCDate()
        ));
        
        dashboard = new this.dashboardModel({
          totalBrokers: 1,
          totalWithdrawals: 0,
          totalBrokerRebate: 0,
          todayBrokers: 1,
          todayWithdrawals: 0,
          todayBrokerRebate: 0,
          yesterdayBrokers: 0,
          yesterdayWithdrawals: 0,
          yesterdayBrokerRebate: 0,
          lastUpdated: todayUTC,
        });
      } else {
        // Check if we need to reset today's stats
        await this.checkAndResetDailyStats(dashboard);
        
        // Increment both total and today's count
        dashboard.totalBrokers += 1;
        dashboard.todayBrokers += 1;
      }

      return await dashboard.save();
    } catch (error) {
      throw new Error(`Failed to increment broker count: ${error.message}`);
    }
  }

  async decrementBrokerCount(): Promise<Dashboard> {
    try {
      const dashboard = await this.dashboardModel.findOne();

      if (dashboard) {
        // Check if we need to reset today's stats
        await this.checkAndResetDailyStats(dashboard);
        
        if (dashboard.totalBrokers > 0) {
          dashboard.totalBrokers -= 1;
          
          // Only decrement today's count if it's greater than 0
          if (dashboard.todayBrokers > 0) {
            dashboard.todayBrokers -= 1;
          }
          
          return await dashboard.save();
        }
      }

      // If no dashboard exists, create one with 0 brokers
      if (!dashboard) {
        const newDashboard = new this.dashboardModel({
          totalBrokers: 0,
          totalWithdrawals: 0,
          totalBrokerRebate: 0,
          todayBrokers: 0,
          todayWithdrawals: 0,
          todayBrokerRebate: 0,
          yesterdayBrokers: 0,
          yesterdayWithdrawals: 0,
          yesterdayBrokerRebate: 0,
          lastUpdated: new Date(),
        });
        return await newDashboard.save();
      }

      return dashboard;
    } catch (error) {
      throw new Error(`Failed to decrement broker count: ${error.message}`);
    }
  }

  async addBrokerRebate(amount: number): Promise<Dashboard> {
    try {
      let dashboard = await this.dashboardModel.findOne();

      if (!dashboard) {
        dashboard = new this.dashboardModel({
          totalBrokers: 0,
          totalWithdrawals: 0,
          totalBrokerRebate: amount,
          todayBrokers: 0,
          todayWithdrawals: 0,
          todayBrokerRebate: amount,
          yesterdayBrokers: 0,
          yesterdayWithdrawals: 0,
          yesterdayBrokerRebate: 0,
          lastUpdated: new Date(),
        });
      } else {
        // Check if we need to reset today's stats
        await this.checkAndResetDailyStats(dashboard);
        
        // Add to both total and today's rebate
        dashboard.totalBrokerRebate += amount;
        dashboard.todayBrokerRebate += amount;
      }

      return await dashboard.save();
    } catch (error) {
      throw new Error(`Failed to add broker rebate: ${error.message}`);
    }
  }

  async addWithdrawal(amount: number): Promise<Dashboard> {
    try {
      let dashboard = await this.dashboardModel.findOne();

      if (!dashboard) {
        dashboard = new this.dashboardModel({
          totalBrokers: 0,
          totalWithdrawals: amount,
          totalBrokerRebate: 0,
          todayBrokers: 0,
          todayWithdrawals: amount,
          todayBrokerRebate: 0,
          yesterdayBrokers: 0,
          yesterdayWithdrawals: 0,
          yesterdayBrokerRebate: 0,
          lastUpdated: new Date(),
        });
      } else {
        // Check if we need to reset today's stats
        await this.checkAndResetDailyStats(dashboard);
        
        // Add to both total and today's withdrawals
        dashboard.totalWithdrawals += amount;
        dashboard.todayWithdrawals += amount;
      }

      return await dashboard.save();
    } catch (error) {
      throw new Error(`Failed to add withdrawal: ${error.message}`);
    }
  }

  findAll() {
    return this.getDashboardStats();
  }

  findOne(id: number) {
    return `This action returns a #${id} dashboard`;
  }

  remove(id: number) {
    return `This action removes a #${id} dashboard`;
  }
}
