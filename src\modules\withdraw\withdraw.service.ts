import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateWithdrawDto, UpdateWithdrawStatusDto } from './dto/withdraw.dto';
import { Withdraw } from './entities/withdraw.entity';
import { Auth } from '../auth/entities/auth.entity';
import { CreditDebitHistory } from '../profit/entities/credit-debit-history';
import { executeAirDrop } from '../../utils/helper';
import { executeSolanaTransfer } from '../../utils/solana-helper';
import { executeBscAirDrop } from '../../utils/bsc-helper';
import { DashboardService } from '../dashboard/dashboard.service';
import { UserDashboardService } from '../user-dashboard/user-dashboard.service';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class WithdrawService {
  constructor(
    @InjectModel(Withdraw.name) private withdrawModel: Model<Withdraw>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
    @InjectModel(CreditDebitHistory.name)
    private creditDebitHistoryModel: Model<CreditDebitHistory>,
    private dashboardService: DashboardService,
    private userDashboardService: UserDashboardService,
    private notificationsService: NotificationsService,
  ) {}

  async create(createWithdrawDto: CreateWithdrawDto, userId: string) {
    console.log('💰 Creating withdraw request for user:', userId);

    // Check user balance
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role == 'admin') {
      throw new BadRequestException('Admin cannot withdraw');
    }

    if (user.balance <= 50) {
      throw new BadRequestException(
        'Minimum balance of 50 is required for withdrawal',
      );
    }

    if (user.balance < createWithdrawDto.amount) {
      throw new BadRequestException('Insufficient balance for withdrawal');
    }

    // Deduct amount from user balance immediately
    const balanceBefore = user.balance;
    user.balance -= createWithdrawDto.amount;
    await user.save();

    // Update user dashboard balance
    await this.userDashboardService.updateUserBalance(userId, user.balance);

    // Create withdraw request
    const withdraw = new this.withdrawModel({
      userId,
      amount: createWithdrawDto.amount,
      walletAddress: createWithdrawDto.walletAddress,
      withdrawCurrency: createWithdrawDto.withdrawCurrency,
      status: 'pending',
      fee: createWithdrawDto.fee,
      withdrawMethod: createWithdrawDto.withdrawMethod,
    });

    const savedWithdraw = await withdraw.save();
    console.log('✅ Withdraw request created:', savedWithdraw._id);

    return {
      message: 'Withdraw request submitted successfully',
      withdrawId: savedWithdraw._id,
      status: 'pending',
    };
  }

  async findAll(adminId: string) {
    console.log('📋 Getting all withdraw requests');

    // Check if user is admin
    const admin = await this.authModel.findById(adminId);
    if (!admin || admin.role !== 'admin') {
      throw new BadRequestException(
        'Only admin can view all withdraw requests',
      );
    }

    const withdraws = await this.withdrawModel
      .find({ status: 'pending' })
      .populate('userId', 'firstName lastName email')
      .populate('processedBy', 'firstName lastName email')
      .sort({ createdAt: -1 });

    return withdraws;
  }

  async findUserWithdraws(userId: string) {
    console.log('📋 Getting withdraw requests for user:', userId);

    const withdraws = await this.withdrawModel
      .find({ userId })
      .sort({ createdAt: -1 });

    return withdraws;
  }

  async findUserWithdrawsByAdmin(userId: string, adminId: string) {
    console.log('📋 Admin getting withdraw requests for user:', userId);

    // Check if user is admin
    const admin = await this.authModel.findById(adminId);
    if (!admin || admin.role !== 'admin') {
      throw new BadRequestException(
        'Only admin can view user withdraw requests',
      );
    }

    // Check if target user exists
    const user = await this.authModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.role === 'admin') {
      throw new BadRequestException('Cannot view admin user withdraws');
    }

    const withdraws = await this.withdrawModel
      .find({ userId })
      .populate('userId', 'firstName lastName email')
      .populate('processedBy', 'firstName lastName email')
      .sort({ createdAt: -1 });

    return withdraws;
  }

  async getAdminUserWithdraws(userId: string, adminId: string) {
    console.log('📋 Admin getting all withdraw requests for user:', userId);
    
    // This is essentially the same as findUserWithdrawsByAdmin
    return this.findUserWithdrawsByAdmin(userId, adminId);
  }

  async findOne(id: string) {
    console.log('🔍 Getting withdraw request:', id);

    const withdraw = await this.withdrawModel
      .findById(id)
      .populate('userId', 'firstName lastName email balance')
      .populate('processedBy', 'firstName lastName email');

    if (!withdraw) {
      throw new NotFoundException('Withdraw request not found');
    }

    return withdraw;
  }

  async updateStatus(
    id?: string,
    updateStatusDto?: UpdateWithdrawStatusDto,
    adminId?: string,
  ) {
    console.log('🔄 Processing withdraw requests...');

    // Check if user is admin
    if (adminId) {
      const admin = await this.authModel.findById(adminId);
      if (!admin || admin.role !== 'admin') {
        throw new BadRequestException('Only admin can update withdraw status');
      }
    }

    // Handle rejection case
    if (id && updateStatusDto?.status === 'rejected') {
      const withdraw = await this.withdrawModel
        .findById(id)
        .populate('userId', 'firstName lastName email balance');
      
      if (!withdraw) {
        throw new NotFoundException('Withdraw request not found');
      }
      
      if (withdraw.status !== 'pending') {
        throw new BadRequestException('Only pending withdrawals can be updated');
      }

      // Restore user balance
      const user = await this.authModel.findById(withdraw.userId);
      if(!user){
        throw new NotFoundException('User not found');
      }
      if (user) {
        user.balance += withdraw.amount;
        await user.save();
        
        // Update user dashboard balance
        await this.userDashboardService.updateUserBalance(user._id.toString(), user.balance);
      }

      // Update withdraw status to rejected
      withdraw.status = 'rejected';
      withdraw.processedAt = new Date();
      withdraw.processedBy = adminId as any;
      withdraw.rejectionReason = updateStatusDto.rejectionReason || '';
      await withdraw.save();

      // Create withdrawal rejection notification
      await this.notificationsService.createWithdrawalNotification(
        user._id.toString(),
        'rejected',
        withdraw.amount,
        id
      );

      return {
        message: 'Withdrawal rejected and balance restored',
        withdrawId: id,
        status: 'rejected',
      };
    }

    let withdrawsToProcess: any[] = [];

    if (id) {
      console.log('🔍 Processing single withdraw:', id);
      const withdraw = await this.withdrawModel
        .findById(id)
        .populate('userId', 'firstName lastName email balance walletAddress');
      if (!withdraw) {
        throw new NotFoundException('Withdraw request not found');
      }
      if (withdraw.status !== 'pending') {
        throw new BadRequestException(
          'Only pending withdrawals can be updated',
        );
      }
      withdrawsToProcess = [withdraw];
    } else {
      // Bulk processing - get all pending withdraws
      console.log('📋 Processing all pending withdraws...');
      withdrawsToProcess = await this.withdrawModel
        .find({ status: 'pending' })
        .populate('userId', 'firstName lastName email balance walletAddress')
        .sort({ createdAt: 1 });

      if (withdrawsToProcess.length === 0) {
        return {
          message: 'No pending withdrawals found',
          processedCount: 0,
          results: [],
        };
      }
    }

    console.log(`🚀 Found ${withdrawsToProcess.length} withdraw(s) to process`);

    // Prepare data for transfers - separate by withdrawal method
    const solRecipients: string[] = [];
    const solAmounts: string[] = [];
    const ethRecipients: string[] = [];
    const ethAmounts: string[] = [];
    const bscRecipients: string[] = []; // New array for BSC
    const bscAmounts: string[] = []; // New array for BSC
    const solWithdrawIds: string[] = [];
    const ethWithdrawIds: string[] = [];
    const bscWithdrawIds: string[] = []; // New array for BSC

    for (const withdraw of withdrawsToProcess) {
      if (!withdraw.walletAddress) {
        console.log(`⚠️ Skipping withdraw ${withdraw._id} - missing wallet address`);
        continue;
      }

      if (withdraw.withdrawMethod === 'sol') {
        solRecipients.push(withdraw.walletAddress);
        const receivedAmount = withdraw.amount - withdraw.fee;
        solAmounts.push(receivedAmount.toString());
        solWithdrawIds.push(withdraw._id.toString());
      } else if (withdraw.withdrawMethod === 'bsc') { // Handle BSC separately
        bscRecipients.push(withdraw.walletAddress);
        const receivedAmount = withdraw.amount - withdraw.fee;
        bscAmounts.push(receivedAmount.toString());
        bscWithdrawIds.push(withdraw._id.toString());
      } else {
        // ETH
        ethRecipients.push(withdraw.walletAddress);
        const receivedAmount = withdraw.amount - withdraw.fee;
        ethAmounts.push(receivedAmount.toString());
        ethWithdrawIds.push(withdraw._id.toString());
      }
    }

    // Check if we have any valid withdrawals
    if (solRecipients.length === 0 && ethRecipients.length === 0 && bscRecipients.length === 0) {
      throw new BadRequestException(
        'No valid withdrawals found with wallet addresses',
      );
    }

    console.log(`💰 Found ${solRecipients.length} SOL, ${ethRecipients.length} ETH, and ${bscRecipients.length} BSC withdrawals`);

    try {
      const results: any[] = [];
      
      // Process SOL withdrawals if any
      if (solRecipients.length > 0) {
        console.log('💰 Executing Solana transfer...');
        const solResult = await executeSolanaTransfer(solRecipients, solAmounts);
        
        if (!solResult.success) {
          throw new Error(`Solana transfer failed: ${solResult.message}`);
        }
        
        console.log('✅ Solana transfer successful! Transaction hash:', solResult.txHash);
        
        // Process successful SOL withdrawals
        await this.processSuccessfulWithdraws(solWithdrawIds, withdrawsToProcess, solResult.txHash, adminId, results);
      }
      
      // Process ETH withdrawals if any
      if (ethRecipients.length > 0) {
        console.log('💰 Executing AirDrop for ETH...');
        const ethResult = await executeAirDrop(ethRecipients, ethAmounts);
        
        if (!ethResult.success) {
          throw new Error(`ETH transfer failed: ${ethResult.message}`);
        }
        
        console.log('✅ ETH transfer successful! Transaction hash:', ethResult.txHash);
        
        // Process successful ETH withdrawals
        await this.processSuccessfulWithdraws(ethWithdrawIds, withdrawsToProcess, ethResult.txHash, adminId, results);
      }

      // Process BSC withdrawals if any
      if (bscRecipients.length > 0) {
        console.log('💰 Executing AirDrop for BSC...');
        const bscResult = await executeBscAirDrop(bscRecipients, bscAmounts);
        
        if (!bscResult.success) {
          throw new Error(`BSC transfer failed: ${bscResult.message}`);
        }
        
        console.log('✅ BSC transfer successful! Transaction hash:', bscResult.txHash);
        
        // Process successful BSC withdrawals
        await this.processSuccessfulWithdraws(bscWithdrawIds, withdrawsToProcess, bscResult.txHash, adminId, results);
      }

      return {
        message: `Successfully processed ${results.length} withdrawal(s)`,
        processedCount: results.length,
        results: results,
      };
    } catch (error) {
      console.error('Error processing withdrawals:', error);
      throw new Error('Error processing withdrawals');
    }
  }

  // Helper method to process successful withdrawals
  private async processSuccessfulWithdraws(
    withdrawIds: string[],
    allWithdraws: any[],
    txHash: string | undefined,
    adminId: string | undefined,
    results: any[]
  ) {
    for (const withdrawId of withdrawIds) {
      const withdraw = allWithdraws.find(
        (w) => w._id.toString() === withdrawId,
      );

      if (!withdraw) continue;

      const user = await this.authModel.findById(withdraw.userId._id);
      if (!user) {
        console.log(`⚠️ User not found for withdraw ${withdrawId}`);
        continue;
      }

      // No need to deduct balance here as it's already deducted during creation
      user.totalWithdrawn += withdraw.amount - withdraw.fee;
      await user.save();

      await this.dashboardService.addWithdrawal(withdraw.amount);

      // Update withdraw status
      withdraw.status = 'completed';
      withdraw.processedAt = new Date();
      withdraw.processedBy = adminId as any;
      withdraw.transactionHash = txHash || 'manual-process';
      withdraw.receivedAmount = withdraw.amount - withdraw.fee;
      await withdraw.save();

      // Create withdrawal success notification
      await this.notificationsService.createWithdrawalNotification(
        user._id.toString(),
        'success',
        withdraw.receivedAmount,
        withdrawId
      );

      // Create credit-debit history record
      const historyRecord = new this.creditDebitHistoryModel({
        userId: user._id,
        type: 'debit',
        amount: withdraw.receivedAmount,
        firstname: user.firstName,
        lastname: user.lastName,
        accountNo: withdraw.walletAddress,
        source: 'withdraw',
        currency: withdraw.withdrawCurrency.toLowerCase(),
        description: `Withdrawal completed via ${withdraw.withdrawMethod} - ${withdraw.withdrawCurrency} ${withdraw.amount} (Fee: ${withdraw.fee}, Received: ${withdraw.receivedAmount}) | TxHash: ${txHash || 'manual-process'}`,
        balanceBefore: user.balance + withdraw.amount, // Since balance was already deducted
        balanceAfter: user.balance,
        from: "admin",
        to: withdraw.walletAddress,
      });
      await historyRecord.save();

      results.push({
        withdrawId: withdrawId,
        userId: user._id,
        amount: withdraw.amount,
        walletAddress: withdraw.walletAddress,
        status: 'completed',
        transactionHash: txHash || 'manual-process',
        withdrawMethod: withdraw.withdrawMethod,
      });

      console.log(
        `✅ Processed ${withdraw.withdrawMethod} withdraw ${withdrawId} for user ${user.email}`,
      );
    }
  }

  async remove(id: string) {
    console.log('🗑️ Removing withdraw request:', id);
    
    const withdraw = await this.withdrawModel.findById(id);
    if (!withdraw) {
      throw new NotFoundException('Withdraw request not found');
    }
    
    // Only allow removing pending withdrawals
    if (withdraw.status !== 'pending') {
      throw new BadRequestException('Only pending withdrawals can be removed');
    }
    
    await this.withdrawModel.findByIdAndDelete(id);
    return { message: 'Withdraw request removed successfully' };
  }
}
