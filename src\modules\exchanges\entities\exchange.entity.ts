import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// Interface for reviews structure
interface ReviewItem {
  mainTitle: string;
  content: Array<{
    title: string;
    content: string;
  }>;
}

// Interface for content structure
interface ContentItem {
  title: string;
  content: string[];
}

interface cashback {
  title: string;
  content: string[];
  ctaLink: string;
  ctaTitle: string;
}

// Interface for FAQ structure
interface FAQItem {
  question: string;
  answer: string;
}

// Interface for user reviews structure
interface UserReviewItem {
  name: string;
  rating: number;
  title: string;
  date: string;
  description: string;
}



@Schema({ timestamps: true })
export class Exchange extends Document {
  @Prop({ required: true  , default: '' })
  title: string;

  @Prop({ required: true , default: '' })
  image: string;

  @Prop({ required: true, default: '' })
  details: string;


  // Connected users array similar to broker
  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth' },
      accountId: { type: MongooseSchema.Types.ObjectId, ref: 'ManageAccount' },
      accountNo: { type: String },
      accountType: { type: String }
    }],
    default: []
  })
  connectedUsers: Array<{
    userId: MongooseSchema.Types.ObjectId;
    accountId: MongooseSchema.Types.ObjectId;
    accountNo: string;
    accountType: string;
  }>;

  // Rating system similar to broker
  @Prop({ default: 0, min: 0, max: 5 })
  ratings: number;

  @Prop({ default: 0 })
  totalRatings: number;

  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth', required: false },
      rating: { type: Number, required: false, min: 1, max: 5 },
      review: { type: String, default: '' },
      ratedAt: { type: Date, default: Date.now },
      userName: { type: String, default: '' }
    }],
    default: []
  })
  rateExchange: Array<{
    userId: MongooseSchema.Types.ObjectId;
    rating: number;
    review: string;
    ratedAt: Date;
    userName: string;
  }>;

  // Reviews array with mainTitle and multiple title/content objects
  @Prop({
    type: [{
      mainTitle: { type: String, required: true },
      content: [{
        title: { type: String, required: true },
        content: { type: String, required: true }
      }]
    }],
    default: []
  })

  
  reviews: ReviewItem[];

  // Social links object
  @Prop({
    type: {
      twitterLink: { type: String, default: '' },
      facebook: { type: String, default: '' },
      instagram: { type: String, default: '' },
      linkedInLink: { type: String, default: '' }
    },
    default: {
      twitterLink: '',
      facebook: '',
      instagram: '',
      linkedInLink: ''
    }
  })
  socialLinks: {
    twitterLink: string;
    facebook: string;
    instagram: string;
    linkedInLink: string;
  };

  @Prop({ type: String, default: 'draft' })
  status: string;

  @Prop({ default: null })
  userId: string;

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  generalInfo: ContentItem[];

  @Prop({
      type: [{
        title: { type: String, required: false },
        content: [{ type: String, required: false }]
      }],
      default: []
    })
    AccountOptions: ContentItem[];


    @Prop({
        type: [{
          title: { type: String, required: false },
          content: [{ type: String, required: false }]
        }],
        default: []
      })
      feeAndFunding: ContentItem[];

      @Prop({
          type: [{
            title: { type: String, required: false },
            content: [{ type: String, required: false }]
          }],
          default: []
        })
        trading: ContentItem[];

        @Prop({
            type: [{
              title: { type: String, required: false },
              content: [{ type: String, required: false }]
            }],
            default: []
          })
          platform: ContentItem[];

          @Prop({
              type: [{
                title: { type: String, required: false },
                content: [{ type: String, required: false }]
              }],
              default: []
            })
            customerService: ContentItem[];


            @Prop({
                type: [{
                  title: { type: String, required: false },
                  content: [{ type: String, required: false }],
                  ctaLink:{type: String, required: false},
                  ctaTitle:{type: String, required: false}

                }],
                default: []
              })
              newCashback: cashback[];


              @Prop({
                type: [{
                  title: { type: String, required: false },
                  content: [{ type: String, required: false }],
                  ctaLink:{type: String, required: false},
                  ctaTitle:{type: String, required: false}

                }],
                default: []
              })
              existingCashback: cashback[];


                @Prop({ type: String, default: '' })
                cashbackData: string;

                // Cashback FAQ array
                @Prop({
                  type: [{
                    question: { type: String, required: false },
                    answer: { type: String, required: false }
                  }],
                  default: []
                })
                cashbackFAQ: FAQItem[];

                @Prop({
                  type: [{
                    question: { type: String, required: false },
                    answer: { type: String, required: false }
                  }],
                  default: []
                })
                exchangeFAQ: FAQItem[];

                // User Reviews array
                @Prop({
                  type: [{
                    name: { type: String, required: false },
                    rating: { type: Number, required: false, min: 1, max: 5 },
                    title: { type: String, required: false },
                    date: { type: String, required: false },
                    description: { type: String, required: false }
                  }],
                  default: []
                })
                Reviews: UserReviewItem[];

  // Add totalRebate field to Exchange entity
  @Prop({ default: 0 })
  totalRebate: number;

  @Prop({ default: 0 })
  profitPercentage: number;

  @Prop({ default: 0 })
  cashbackLink: string;

  @Prop({ default: 0 })
  description: string;



}

export const ExchangeSchema = SchemaFactory.createForClass(Exchange);
