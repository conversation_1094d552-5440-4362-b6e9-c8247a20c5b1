import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { FaqsService } from './faqs.service';
import { CreateFaqDto, UpdateFaqDto } from './dto/create-faq.dto';
import { Public } from '../../Gaurd/public.decorator';

@Public()
@ApiTags('FAQs')
@Controller('faqs')
export class FaqsController {
  constructor(private readonly faqsService: FaqsService) {}

  @Post('createFAQS')
  @ApiOperation({ summary: 'Create a new FAQ' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'FAQ has been successfully created.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
  })
  async create(@Body() createFaqDto: CreateFaqDto) {
    const faq = await this.faqsService.createFAQs(createFaqDto);
    return {
      status: HttpStatus.CREATED,
      data: faq,
    };
  }

  @Public()
  @Get('getAllFAQs')
  @ApiOperation({ summary: 'Get all FAQs' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all FAQs retrieved successfully.',
  })
  async findAll() {
    const faqs = await this.faqsService.findAll();
    return {
      status: HttpStatus.OK,
      data: faqs,
    };
  }

  @Public()
  @Get('faqById/:id')
  @ApiOperation({ summary: 'Get FAQ by ID' })
  @ApiParam({
    name: 'id',
    description: 'FAQ ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'FAQ retrieved successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'FAQ not found.',
  })
  async findOne(@Param('id') id: string) {
    const faq = await this.faqsService.findOne(id);
    return {
      status: HttpStatus.OK,
      data: faq,
    };
  }

  @Public()
  @Put('updateFAQ/:id')
  @ApiOperation({ summary: 'Update FAQ by ID' })
  @ApiParam({
    name: 'id',
    description: 'FAQ ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'FAQ updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'FAQ not found.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
  })
  async update(@Param('id') id: string, @Body() updateFaqDto: UpdateFaqDto) {
    const faq = await this.faqsService.update(id, updateFaqDto);
    return {
      status: HttpStatus.OK,
      data: faq,
    };
  }

  @Public()
  @Delete('deleteFAQ/:id')
  @ApiOperation({ summary: 'Delete FAQ by ID' })
  @ApiParam({
    name: 'id',
    description: 'FAQ ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'FAQ deleted successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'FAQ not found.',
  })
  async remove(@Param('id') id: string) {
    const result = await this.faqsService.remove(id);
    return {
      status: HttpStatus.OK,
      ...result,
    };
  }
}
