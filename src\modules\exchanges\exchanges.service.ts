import {
  Injectable,
  NotFoundException,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Exchange } from './entities/exchange.entity';
import {
  CreateExchangeDto,
  UpdateExchangeDto,
  RateExchangeDto,
} from './dto/exchange.dto';
import { Auth } from '../auth/entities/auth.entity';

@Injectable()
export class ExchangesService {
  constructor(
    @InjectModel(Exchange.name) private exchangeModel: Model<Exchange>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
  ) {}

  async create(
    createExchangeDto: CreateExchangeDto,
    userId: string,
  ): Promise<Exchange> {
    try {
      const user = await this.authModel.findById(userId);
      if (user?.role !== 'admin') {
        throw new UnauthorizedException(
          'You are not authorized to create an exchange',
        );
      }

      // Check if this is an update (if exchange ID exists in request body)
      if (createExchangeDto['id']) {
        const exchangeId = createExchangeDto['id'];
        const existingExchange = await this.exchangeModel.findById(exchangeId);

        if (!existingExchange) {
          throw new NotFoundException(`Exchange with ID ${exchangeId} not found`);
        }

        // Update existing exchange
        const updatedExchange = await this.exchangeModel.findByIdAndUpdate(
          exchangeId,
          { ...createExchangeDto, userId },
          { new: true, runValidators: true }
        );

        if (!updatedExchange) {
          throw new NotFoundException(`Failed to update exchange with ID ${exchangeId}`);
        }

        // Check if all required fields are filled to activate
        const isComplete = this.checkExchangeCompletion(updatedExchange);
        if (isComplete && updatedExchange.status === 'draft') {
          updatedExchange.status = 'active';
          await updatedExchange.save();
        }

        return updatedExchange;
      }

      // Check if exchange with same title already exists (only for new exchanges)
      if (createExchangeDto.title) {
        const existingExchange = await this.exchangeModel.findOne({
          title: createExchangeDto.title,
        });

        if (existingExchange) {
          throw new ConflictException('Exchange with this title already exists');
        }
      }

      // Create new exchange
      const createdExchange = new this.exchangeModel({
        ...createExchangeDto,
        userId,
        status: 'draft'
      });
      const savedExchange = await createdExchange.save();

      return savedExchange;
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new Error(`Failed to create exchange: ${error.message}`);
    }
  }

  // Helper method to check if exchange is complete
  private checkExchangeCompletion(exchange: any): boolean {
    return !!(
      exchange.title &&
      exchange.image &&
      exchange.details &&
      exchange.generalInfo?.length > 0 &&
      exchange.AccountOptions?.length > 0 &&
      exchange.feeAndFunding?.length > 0 &&
      exchange.trading?.length > 0 &&
      exchange.platform?.length > 0 &&
      exchange.customerService?.length > 0
    );
  }

  async findAll(): Promise<Exchange[]> {
    try {
      return await this.exchangeModel
        .find({ status: 'active' })
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      throw new Error(`Failed to fetch exchanges: ${error.message}`);
    }
  }

  async findAllAdmin(userId: string): Promise<Exchange[]> {
    try {
      const user = await this.authModel.findById(userId);
      if (!user || user.role !== 'admin') {
        throw new UnauthorizedException('Admin access required');
      }

      return await this.exchangeModel
        .find()
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new Error(`Failed to fetch exchanges: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<Exchange> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new NotFoundException(`Invalid exchange ID: ${id}`);
      }

      const exchange = await this.exchangeModel.findById(id).exec();
      if (!exchange) {
        throw new NotFoundException(`Exchange with ID ${id} not found`);
      }

      return exchange;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch exchange: ${error.message}`);
    }
  }

  async update(
    id: string,
    updateExchangeDto: UpdateExchangeDto,
    userId: string,
  ): Promise<Exchange> {
    try {
      const user = await this.authModel.findById(userId);
      if (user?.role !== 'admin') {
        throw new UnauthorizedException(
          'You are not authorized to update an exchange',
        );
      }

      const existingExchange = await this.exchangeModel.findById(id);
      if (!existingExchange) {
        throw new NotFoundException(`Exchange with ID ${id} not found`);
      }

      // If updating title, check for conflicts
      if (updateExchangeDto.title) {
        const exchangeWithSameTitle = await this.exchangeModel.findOne({
          title: updateExchangeDto.title,
          _id: { $ne: id },
        });

        if (exchangeWithSameTitle) {
          throw new ConflictException('Exchange with this title already exists');
        }
      }

      // Use $set to only update provided fields, keeping others unchanged
      const updatedExchange = await this.exchangeModel
        .findByIdAndUpdate(
          id,
          { $set: updateExchangeDto },
          { new: true, runValidators: true },
        )
        .exec();

      if (!updatedExchange) {
        throw new NotFoundException(`Exchange with ID ${id} not found`);
      }

      // Check if all required fields are filled to activate
      const isComplete = this.checkExchangeCompletion(updatedExchange);
      if (isComplete && updatedExchange.status === 'draft') {
        updatedExchange.status = 'active';
        await updatedExchange.save();
      }

      return updatedExchange;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to update exchange: ${error.message}`);
    }
  }

  async rateExchange(
    exchangeId: string,
    rateExchangeDto: RateExchangeDto,
    userId: string,
  ): Promise<Exchange> {
    try {
      const user = await this.authModel.findById(userId);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      const exchange = await this.exchangeModel.findById(exchangeId);
      if (!exchange) {
        throw new NotFoundException(`Exchange with ID ${exchangeId} not found`);
      }

      // Check if user is connected to this exchange
      console.log(`🔍 Checking if user ${userId} is connected to exchange ${exchangeId}`);
      console.log(`📋 Exchange connectedUsers:`, exchange.connectedUsers);

      const isConnected = exchange.connectedUsers.some(
        (connectedUser) => {
          const connectedUserId = connectedUser.userId.toString();
          console.log(`🔍 Comparing: ${connectedUserId} === ${userId}`);
          console.log(`🔍 Types: ${typeof connectedUserId} === ${typeof userId}`);
          return connectedUserId === userId || connectedUserId === userId.toString();
        }
      );

      console.log(`✅ User connected status: ${isConnected}`);

      if (!isConnected) {
        throw new BadRequestException(
          'You must be connected to this exchange to rate it',
        );
      }

      // Check if user has already rated this exchange
      console.log(`🔍 Looking for existing rating by user: ${userId}`);
      console.log(`📋 Current ratings:`, exchange.rateExchange.map(r => ({ userId: r.userId.toString(), rating: r.rating })));

      const existingRatingIndex = exchange.rateExchange.findIndex(
        (rating) => {
          const ratingUserId = rating.userId.toString();
          console.log(`🔍 Comparing rating userId: ${ratingUserId} with current userId: ${userId}`);
          return ratingUserId === userId.toString();
        }
      );

      console.log(`📍 Existing rating index: ${existingRatingIndex}`);

      if (existingRatingIndex !== -1) {
        // Update existing rating
        console.log(`✏️ Updating existing rating at index ${existingRatingIndex}`);
        exchange.rateExchange[existingRatingIndex].rating = rateExchangeDto.rating;
        exchange.rateExchange[existingRatingIndex].infoRating = rateExchangeDto.infoRating;
        exchange.rateExchange[existingRatingIndex].regulationsRating = rateExchangeDto.regulationsRating;
        exchange.rateExchange[existingRatingIndex].review = rateExchangeDto.review || '';
        exchange.rateExchange[existingRatingIndex].ratedAt = new Date();
        exchange.rateExchange[existingRatingIndex].userName = `${user.firstName} ${user.lastName}`;
      } else {
        // Add new rating
        console.log(`➕ Adding new rating for user ${userId}`);
        exchange.rateExchange.push({
          userId: userId as any,
          rating: rateExchangeDto.rating,
          infoRating: rateExchangeDto.infoRating,
          regulationsRating: rateExchangeDto.regulationsRating,
          review: rateExchangeDto.review || '',
          ratedAt: new Date(),
          userName: `${user.firstName} ${user.lastName}`,
        });
        exchange.totalRatings += 1;
      }

      // Calculate average ratings for all three types
      const totalRating = exchange.rateExchange.reduce(
        (sum, rating) => sum + rating.rating,
        0,
      );
      const totalInfoRating = exchange.rateExchange.reduce(
        (sum, rating) => sum + rating.infoRating,
        0,
      );
      const totalRegulationsRating = exchange.rateExchange.reduce(
        (sum, rating) => sum + rating.regulationsRating,
        0,
      );

      exchange.ratings = totalRating / exchange.rateExchange.length;
      exchange.infoRating = totalInfoRating / exchange.rateExchange.length;
      exchange.regulationsRating = totalRegulationsRating / exchange.rateExchange.length;

      const updatedExchange = await exchange.save();
      return updatedExchange;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to rate exchange: ${error.message}`);
    }
  }

  async remove(id: string, userId: string): Promise<{ message: string }> {
    try {
      const user = await this.authModel.findById(userId);
      if (user?.role !== 'admin') {
        throw new UnauthorizedException(
          'You are not authorized to delete an exchange',
        );
      }

      const deletedExchange = await this.exchangeModel.findByIdAndDelete(id);
      if (!deletedExchange) {
        throw new NotFoundException(`Exchange with ID ${id} not found`);
      }

      return { message: 'Exchange deleted successfully' };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to delete exchange: ${error.message}`);
    }
  }
}
