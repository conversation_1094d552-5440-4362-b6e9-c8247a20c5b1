import { ethers } from 'ethers';

// AirDrop Contract ABI - Only the functions we need
const BSC_AIRDROP_ABI = [
  "function airDrop(address[] calldata _recipients, uint256[] calldata _amount) external",
  "function usdtToken() external view returns (address)",
  "function FundsWallet() external view returns (address)",
  "function owner() external view returns (address)"
];

// ERC20 Token ABI for token operations
const BEP20_ABI = [
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function balanceOf(address account) external view returns (uint256)"
];

// AirDrop function to transfer BEP20 tokens to multiple users on BSC
export async function executeBscAirDrop(recipients: string[], amounts: string[]): Promise<{ success: boolean; message: string; txHash?: string }> {
  try {
    // Validate inputs
    if (!recipients || !amounts || recipients.length === 0 || amounts.length === 0) {
      throw new Error('Recipients and amounts arrays cannot be empty');
    }

    if (recipients.length !== amounts.length) {
      throw new Error('Recipients and amounts arrays must have the same length');
    }

    // Environment variables
    const privateKey = process.env.BSC_PRIVATE_KEY;
    const walletAddress = process.env.BSC_WALLET_ADDRESS;
    const contractAddress = process.env.BSC_CONTRACT_ADDRESS;

    if (!privateKey || !walletAddress || !contractAddress) {
      throw new Error('BSC_PRIVATE_KEY, BSC_WALLET_ADDRESS, and BSC_CONTRACT_ADDRESS must be set in environment variables');
    }

    // Setup provider and wallet for BSC Testnet - Try multiple RPC endpoints
    const rpcEndpoints = [
      'https://data-seed-prebsc-1-s1.binance.org:8545',
      'https://data-seed-prebsc-2-s1.binance.org:8545',
      'https://data-seed-prebsc-1-s2.binance.org:8545',
      'https://data-seed-prebsc-2-s2.binance.org:8545',
      'https://bsc-testnet.publicnode.com'
    ];

    let provider: ethers.JsonRpcProvider | null = null;

    // Try different RPC endpoints until one works
    for (const rpcUrl of rpcEndpoints) {
      try {
        console.log(`🔄 Trying BSC RPC endpoint: ${rpcUrl}`);
        const testProvider = new ethers.JsonRpcProvider(rpcUrl);

        // Test connection by getting network info
        await testProvider.getNetwork();
        console.log(`✅ Connected successfully to: ${rpcUrl}`);
        provider = testProvider;
        break;
      } catch (error) {
        console.log(`❌ Failed to connect to: ${rpcUrl}`);
        continue;
      }
    }

    if (!provider) {
      throw new Error('Failed to connect to any BSC RPC endpoint. Please try again later.');
    }
    const wallet = new ethers.Wallet(privateKey, provider);

    // Create contract instance
    const contract = new ethers.Contract(contractAddress, BSC_AIRDROP_ABI, wallet);

    // Convert amounts to Wei (assuming amounts are in USDT with 18 decimals for BEP20)
    const amountsInWei = amounts.map(amount => ethers.parseUnits(amount, 18)); // BEP20 typically has 18 decimals

    // Calculate total amount needed
    const totalAmount = amountsInWei.reduce((sum, amount) => sum + amount, BigInt(0));

    console.log('🚀 Starting BSC AirDrop process...');
    console.log('📊 Recipients:', recipients.length);
    console.log('💰 Total amount:', ethers.formatUnits(totalAmount, 18), 'BEP20');

    // Get token address and funds wallet from contract
    const tokenAddress = await contract.usdtToken();
    const fundsWallet = await contract.FundsWallet();
    const contractOwner = await contract.owner();

    console.log('🏦 Token address:', tokenAddress);
    console.log('💼 Funds wallet:', fundsWallet);
    console.log('👤 Contract owner:', contractOwner);

    // Check if our wallet is the owner
    if (walletAddress.toLowerCase() !== contractOwner.toLowerCase()) {
      throw new Error(`Only contract owner can execute airdrops. Owner: ${contractOwner}, Your wallet: ${walletAddress}`);
    }

    // Create token contract instance for balance/allowance checks
    const tokenContract = new ethers.Contract(tokenAddress, BEP20_ABI, wallet);

    // Check funds wallet balance
    const balance = await tokenContract.balanceOf(fundsWallet);
    console.log('💳 Funds wallet balance:', ethers.formatUnits(balance, 18), 'BEP20');

    if (balance < totalAmount) {
      throw new Error(`Insufficient balance in funds wallet. Required: ${ethers.formatUnits(totalAmount, 18)}, Available: ${ethers.formatUnits(balance, 18)}`);
    }

    // Check allowance from funds wallet to contract
    const currentAllowance = await tokenContract.allowance(fundsWallet, contractAddress);
    console.log('🔐 Current allowance:', ethers.formatUnits(currentAllowance, 18), 'BEP20');

    if (currentAllowance < totalAmount) {
      throw new Error(`Insufficient allowance. Required: ${ethers.formatUnits(totalAmount, 18)}, Available: ${ethers.formatUnits(currentAllowance, 18)}. Please approve the contract to spend tokens from funds wallet.`);
    }

    // Execute airdrop
    console.log('🔄 Executing BSC airdrop...');
    const transferTx = await contract.airDrop(recipients, amountsInWei);

    console.log('⏳ Waiting for transaction confirmation...');
    const receipt = await transferTx.wait();

    console.log('✅ BSC AirDrop completed successfully!');
    console.log('🔗 Transaction hash:', receipt.hash);
    console.log('⛽ Gas used:', receipt.gasUsed.toString());
    console.log('💰 Total distributed:', ethers.formatUnits(totalAmount, 18), 'BEP20');

    return {
      success: true,
      message: `BSC AirDrop completed successfully! Distributed ${ethers.formatUnits(totalAmount, 18)} BEP20 to ${recipients.length} recipients`,
      txHash: receipt.hash
    };

  } catch (error) {
    console.error('❌ BSC AirDrop failed:', error);
    return {
      success: false,
      message: `BSC AirDrop failed: ${error.message}`
    };
  }
}

// Example usage function for testing
export async function testBscAirDrop(): Promise<void> {
  // Example data - replace with actual user wallet addresses and amounts
  const recipients = [
    '******************************************', // Example wallet 1
    '******************************************', // Example wallet 2
  ];

  const amounts = [
    '0.01',  // 0.1 BEP20 to user 1
    '0.01',    // 1 BEP20 to user 2
  ];

  console.log('🧪 Testing BSC AirDrop function...');
  console.log('📋 Recipients:', recipients);
  console.log('💰 Amounts:', amounts, 'BEP20');

  const result = await executeBscAirDrop(recipients, amounts);

  if (result.success) {
    console.log('✅ Test successful:', result.message);
    console.log('🔗 Transaction hash:', result.txHash);
  } else {
    console.log('❌ Test failed:', result.message);
  }
}
