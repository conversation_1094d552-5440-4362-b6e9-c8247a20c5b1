import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserDashboardService } from './user-dashboard.service';
import { UserDashboardController } from './user-dashboard.controller';
import { UserDashboard, UserDashboardSchema } from './entities/user-dashboard.entity';
import { UserEarnings, UserEarningsSchema } from './entities/user-earnings.entity';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserDashboard.name, schema: UserDashboardSchema },
      { name: UserEarnings.name, schema: UserEarningsSchema },
      { name: Auth.name, schema: AuthSchema },
    ]),
  ],
  controllers: [UserDashboardController],
  providers: [UserDashboardService],
  exports: [UserDashboardService],
})
export class UserDashboardModule {}
