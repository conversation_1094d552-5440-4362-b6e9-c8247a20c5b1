import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsNotEmpty, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAuthDto {
  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  @MinLength(6)
  password: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  role?: string;

  @IsString()
  loginType: string;

  @IsString()
  @IsOptional()
  refferedBy: string;

  @IsOptional()
  @IsString()
  capcha?: string;
}

export class UpdateAuthDto {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  @MinLength(6)
  password?: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  role?: string;
}

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;
}

export class RefreshTokenDto {
  @IsString()
  refresh_token: string;
}

export class VerifyEmailDto {
  @IsString()
  code: string;

  @IsEmail()
  email: string;
}

export class ForgetPasswordDto {
  @IsEmail()
  email: string;
}

export class ResetPasswordDto {
  @IsString()
  code: string;

  @IsEmail()
  email: string;

  @IsString()
  password: string;
}

export class UpdateEmailDto {
  @IsEmail()
  email: string;
}

export class ChangePasswordDto {
  @IsString()
  @MinLength(6)
  oldPassword: string;

  @IsString()
  @MinLength(6)
  newPassword: string;
}

export class DeactivateAccountDto {
  @IsString()
  @IsOptional()
  status?: string;
}

export class ToggleUserLockDto {
  @IsString()
  userId: string;
}

export class ChangeNameDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;
}

export class CreateManagerDto {
  @ApiProperty({ description: 'Manager email address' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Manager password (minimum 6 characters)' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: 'Manager first name' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Manager last name' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'Array of roles to assign to manager',
    example: ['withdraw', 'user', 'support', 'broker'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  roles: string[];
}

export class UpdateManagerRolesDto {
  @ApiProperty({
    description: 'Array of roles to assign to manager',
    example: ['withdraw', 'user', 'support', 'broker'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  roles: string[];
}
