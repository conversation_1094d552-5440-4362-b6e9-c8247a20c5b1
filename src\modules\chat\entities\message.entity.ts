import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Message extends Document {
  @Prop({ required: true })
  roomId: string;

  @Prop({ required: true, ref: 'Auth' })
  sender: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, ref: 'Auth' })
  receiver: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, ref: 'Support' })
  requesterId: MongooseSchema.Types.ObjectId;

  @Prop({ default: '' })
  message?: string;

  @Prop({ default: false })
  isRead: boolean;

  @Prop({ type: [String], default: [] })
  readUsers: string[];

  @Prop({ default: '' })
  mediaUrl: string;

  @Prop({ default: Date.now })
  timestamp: Date;
}

export const MessageSchema = SchemaFactory.createForClass(Message);
