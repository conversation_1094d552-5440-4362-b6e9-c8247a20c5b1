import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// Interface for content structure
interface ContentItem {
  title: string;
  content: string[];
}



@Schema({ timestamps: true })
export class <PERSON>roker extends Document {

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  generalInfo: ContentItem[];

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  AccountOptions: ContentItem[];

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  customerService: ContentItem[];

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  trading: ContentItem[];

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  account: ContentItem[];

  @Prop({
    type: [{
      title: { type: String, required: false },
      content: [{ type: String, required: false }]
    }],
    default: []
  })
  generalInfoBroker: ContentItem[];

  // Description field - simple string
  @Prop({ type: String, default: '' })
  description: string;

  
  @Prop({ default: null })
  userId: string;

  @Prop({ required: true })
  brokerName: string;

  @Prop({ required: true })
  brokerImage: string;

  // Broker status as simple string
  @Prop({ type: String, default: 'draft' })
  status: string;

  // Referral link for the broker
  // @Prop({ type: String ,default: ''})
  // referralLink: string;

  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth' },
      accountId: { type: MongooseSchema.Types.ObjectId, ref: 'ManageAccount' },
      accountNo: { type: String },
      accountType: { type: String }
    }],
    default: []
  })
  connectedUsers: Array<{
    userId: MongooseSchema.Types.ObjectId;
    accountId: MongooseSchema.Types.ObjectId;
    accountNo: string;
    accountType: string;
  }>;

  @Prop({default: ''})
  csvFile: string;

   @Prop({default: ''})
  brokerType: string;

  // Ratings field - default 0
  @Prop({ default: 0, min: 0, max: 5 })
  ratings: number;

  // Total number of ratings given
  @Prop({ default: 0 })
  totalRatings: number;

  // Array to store individual ratings with user info
  @Prop({
    type: [{
      userId: { type: MongooseSchema.Types.ObjectId, ref: 'Auth', required: false },
      rating: { type: Number, required: false, min: 1, max: 5 },
      review: { type: String, default: '' },
      ratedAt: { type: Date, default: Date.now },
      userName: { type: String, default: '' }
    }],
    default: []
  })
  userRatings: Array<{
    userId: MongooseSchema.Types.ObjectId;
    rating: number;
    review: string;
    ratedAt: Date;
    userName: string;
  }>;

  // Add Account array with title and referralLink
  @Prop({
    type: [{
      title: { type: String, required: false },
      referralLink: { type: String, required: false }
    }],
    default: []
  })
  addAccount: Array<{
    title: string;
    referralLink: string;
  }>;

  // Note field
  @Prop({ type: String, default: '' })
  note: string;

  // Email object with to, subject, body, successMessage, and defaultCC
  @Prop({
    type: {
      to: { type: String, default: '' },
      subject: { type: String, default: '' },
      body: { type: String, default: '' },
      successMessage: { type: String, default: '' },
      defaultCC: { type: String, default: '<EMAIL>' }
    },
    default: {
      to: '',
      subject: '',
      body: '',
      successMessage: '',
      defaultCC: '<EMAIL>'
    }
  })
  email: {
    to: string;
    subject: string;
    body: string;
    successMessage: string;
    defaultCC: string;
  };

  // Broker details file path
  @Prop({ type: String, default: null })
  brokerDetailsFile: string;

  // Add totalRebate field to Broker entity
  @Prop({ default: 0 })
  totalRebate: number;

  // Add isFeatured field to Broker entity
  @Prop({ default: false })
  isFeatured: boolean;

  @Prop({ default: 0 })
  profitPercentage: number;

}

export const BrokerSchema = SchemaFactory.createForClass(Broker);
