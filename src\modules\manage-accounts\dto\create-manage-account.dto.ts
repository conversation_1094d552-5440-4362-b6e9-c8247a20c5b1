import { IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class JoinBrokerDto {
  @ApiProperty({ description: 'Account number for the broker' })
  @IsString()
  accountNo: string;

  @ApiProperty({ description: 'Broker ID to join' })
  @IsString()
  brokerId: string;

  @ApiProperty({ description: 'Account type for the broker' })
  @IsString()
  accountType: string;

  // @ApiProperty({ description: 'Exchange title for the broker' })
  // @IsString()
  // exchangeTitle: string;

  // @ApiProperty({ description: 'Referral link for the broker' })
  // @IsString()
  // referralLink: string;
}

export class JoinExchangeDto {
  @ApiProperty({ description: 'Account number for the exchange' })
  @IsString()
  accountNo: string;

  @ApiProperty({ description: 'Exchange ID to join' })
  @IsString()
  exchangeId: string;

  @ApiProperty({ description: 'Account type for the exchange' })
  @IsString()
  accountType: string;
}

export class UpdateAccountStatusDto {
  @ApiProperty({ description: 'Account ID to update' })
  @IsString()
  accountId: string;

  @ApiProperty({
    description: 'New status for the account',
    enum: ['pending', 'active', 'rejected'],
    example: 'active'
  })
  @IsEnum(['pending', 'active', 'rejected'])
  status: string;

  @ApiProperty({ description: 'Broker ID', required: false })
  @IsOptional()
  @IsString()
  brokerId?: string;
}
