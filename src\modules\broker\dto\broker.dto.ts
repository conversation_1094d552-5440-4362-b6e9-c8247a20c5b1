import {
  IsString,
  IsArray,
  IsOptional,
  ValidateNested,
  ArrayMinSize,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class ContentItemDto {
  @ApiProperty({ description: 'Title of the content item' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Array of content strings', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  content: string[];
}

export class CreateBrokerDto {
  // @ApiProperty({ description: 'User ID' })
  // @IsString()
  // userId: string;

  @ApiProperty({ description: 'Broker type' })
  @IsString()
  @IsOptional()
  brokerType: string;

  @ApiProperty({ description: 'Broker name' })
  @IsString()
  @IsOptional()
  brokerName: string;

  @ApiProperty({
    description: 'General information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfo?: ContentItemDto[];

  @ApiProperty({ description: 'Account options array', type: [ContentItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  AccountOptions?: ContentItemDto[];

  @ApiProperty({
    description: 'Customer service array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  customerService?: ContentItemDto[];

  @ApiProperty({
    description: 'Trading information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  trading?: ContentItemDto[];

  @ApiProperty({
    description: 'Account information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  account?: ContentItemDto[];

  @ApiProperty({
    description: 'General info broker array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfoBroker?: ContentItemDto[];

  @ApiProperty({ description: 'Description text for broker' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Broker image URL' })
  @IsOptional()
  @IsString()
  brokerImage?: string;

  @ApiProperty({ description: 'Broker status', default: 'draft' })
  @IsOptional()
  @IsString()
  status?: string;

  // @ApiProperty({ description: 'Referral link for the broker' })
  // @IsOptional()
  // @IsString()
  // referralLink?: string;

  @ApiProperty({ description: 'CSV file path' })
  @IsOptional()
  @IsString()
  csvFile?: string;

  @ApiProperty({ description: 'Connected users array', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  connectedUsers?: string[];

  @ApiProperty({
    description: 'Add account array with title and referral link',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        referralLink: { type: 'string' },
      },
    },
  })
  @IsOptional()
  @IsArray()
  addAccount?: Array<{
    title: string;
    referralLink: string;
  }>;

  @ApiProperty({ description: 'Note field for broker' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Email configuration object',
    type: 'object',
    properties: {
      to: { type: 'string' },
      subject: { type: 'string' },
      body: { type: 'string' },
      successMessage: { type: 'string' },
      defaultCC: { type: 'string' },
    },
  })
  @IsOptional()
  email?: {
    to?: string;
    subject?: string;
    body?: string;
    successMessage?: string;
    defaultCC?: string;
  };

  @ApiProperty({ description: 'Broker details file path' })
  @IsOptional()
  @IsString()
  brokerDetailsFile?: string;

  @ApiProperty({ description: 'Profit percentage for the broker' })
  @IsOptional()
  @IsNumber()
  profitPercentage?: number;
}

export class UpdateBrokerDto {
  @ApiProperty({ description: 'Broker name' })
  @IsOptional()
  @IsString()
  brokerName?: string;

  @ApiProperty({ description: 'Broker image URL' })
  @IsOptional()
  @IsString()
  brokerImage?: string;

  @ApiProperty({
    description: 'General information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfo?: ContentItemDto[];

  @ApiProperty({ description: 'Account options array', type: [ContentItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  AccountOptions?: ContentItemDto[];

  @ApiProperty({
    description: 'Customer service array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  customerService?: ContentItemDto[];

  @ApiProperty({
    description: 'Trading information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  trading?: ContentItemDto[];

  @ApiProperty({
    description: 'Account information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  account?: ContentItemDto[];

  @ApiProperty({
    description: 'General info broker array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfoBroker?: ContentItemDto[];

  @ApiProperty({ description: 'Description text for broker' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Broker status' })
  @IsOptional()
  @IsString()
  status?: string;

  // @ApiProperty({ description: 'Referral link for the broker' })
  // @IsOptional()
  // @IsString()
  // referralLink?: string;

  @ApiProperty({ description: 'Broker type' })
  @IsOptional()
  @IsString()
  brokerType?: string;

  @ApiProperty({ description: 'CSV file path' })
  @IsOptional()
  @IsString()
  csvFile?: string;

  @ApiProperty({ description: 'Connected users array', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  connectedUsers?: string[];

  @ApiProperty({
    description: 'Add account array with title and referral link',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        referralLink: { type: 'string' },
      },
    },
  })
  @IsOptional()
  @IsArray()
  addAccount?: Array<{
    title: string;
    referralLink: string;
  }>;

  @ApiProperty({ description: 'Note field for broker' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Email configuration object',
    type: 'object',
    properties: {
      to: { type: 'string' },
      subject: { type: 'string' },
      body: { type: 'string' },
      successMessage: { type: 'string' },
      defaultCC: { type: 'string' },
    },
  })
  @IsOptional()
  email?: {
    to?: string;
    subject?: string;
    body?: string;
    successMessage?: string;
    defaultCC?: string;
  };

  @ApiProperty({ description: 'Broker details file path' })
  @IsOptional()
  @IsString()
  brokerDetailsFile?: string;

  @ApiProperty({ description: 'Profit percentage for the broker' })
  @IsOptional()
  @IsNumber()
  profitPercentage?: number;
}

export class RateBrokerDto {
  @ApiProperty({
    description: 'Rating value between 1 and 5',
    minimum: 1,
    maximum: 5,
    example: 4,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

 
  @IsOptional()
  @IsString()
  review?: string;
}

export class SendEmailDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;

  @ApiProperty({
    description: 'Email subject',
    example: 'Important Update',
  })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'CC email addresses (comma separated)',
    example: '<EMAIL>,<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  cc?: string;

  @ApiProperty({
    description: 'Email body content',
    example: 'This is the email body content.',
  })
  @IsString()
  body: string;
}
