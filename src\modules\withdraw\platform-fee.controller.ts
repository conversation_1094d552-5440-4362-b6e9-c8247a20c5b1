
import { Controller, Get, Post, Body, UseGuards, Request, HttpStatus } from '@nestjs/common';
import { PlatformFeeService } from './platform-fee.service';
import { AddPlatformFeeDto } from './dto/platform-fee.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/Gaurd/public.decorator';

@ApiTags('Platform Fee')
@Controller('platform-fee')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PlatformFeeController {
  constructor(private readonly platformFeeService: PlatformFeeService) {}

  @Post('add-fee')
  @ApiOperation({ 
    summary: 'Add or update platform withdrawal fees (Admin only)',
    description: 'Admin can add or update withdrawal fees for different cryptocurrencies. If document exists, it will be updated with provided fields only.'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Platform fees added/updated successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Platform fees updated successfully' },
        data: {
          type: 'object',
          properties: {
            ethFee: { type: 'number', example: 0.005 },
            solFee: { type: 'number', example: 0.01 },
            btcFee: { type: 'number', example: 0.0001 },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Only admin can manage platform fees'
  })
  addFee(@Body() addPlatformFeeDto: AddPlatformFeeDto, @Request() req) {
    return this.platformFeeService.addFee(addPlatformFeeDto, req.user.sub);
  }
  
  @Get('get-fee')
  @ApiOperation({ 
    summary: 'Get current platform withdrawal fees',
    description: 'Retrieve current withdrawal fees for all supported cryptocurrencies'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Platform fees retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Platform fees retrieved successfully' },
        data: {
          type: 'object',
          properties: {
            ethFee: { type: 'number', example: 0.005 },
            solFee: { type: 'number', example: 0.01 },
            btcFee: { type: 'number', example: 0.0001 },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        
       
      }
    }
  })
  getFee(@Request() req) {
    // If request has user token, pass the user ID
    const userId = req.user?.sub || null;
    console.log("i am userId",userId);
    
    return this.platformFeeService.getFee(userId);
  }
}

