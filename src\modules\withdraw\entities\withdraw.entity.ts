import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Withdraw extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  amount: number;

  @Prop({ required: true })
  walletAddress: string;

  @Prop({ required: true })
  withdrawCurrency: string;

  @Prop({ default: 'pending', enum: ['pending', 'completed', 'rejected'] })
  status: string;

  @Prop({ default: Date.now })
  requestedAt: Date;

  @Prop({ default: null })
  processedAt: Date;

  @Prop({ default: null, ref: 'Auth' })
  processedBy: MongooseSchema.Types.ObjectId;

  @Prop({ default: '' })
  rejectionReason: string;

  @Prop({ default: null })
  transactionHash: string;

  @Prop({ default: 0 })
  fee: number;

  @Prop({ default: 'airdrop', enum: ['eth', 'sol', 'bsc'] })
  withdrawMethod: string;

  @Prop({ default: 0 })
  receivedAmount: number;

}

export const WithdrawSchema = SchemaFactory.createForClass(Withdraw);
