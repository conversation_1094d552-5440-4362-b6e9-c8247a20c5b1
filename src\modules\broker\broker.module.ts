import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BrokerService } from './broker.service';
import { BrokerController } from './broker.controller';
import { Broker, BrokerSchema } from './entity/broker';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';
import {
  ManageAccount,
  ManageAccountSchema,
} from '../manage-accounts/entities/manage-account.entity';
import { MailService } from '../../utils/sendMail';
import { DashboardModule } from '../dashboard/dashboard.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Broker.name, schema: BrokerSchema },
      { name: Auth.name, schema: AuthSchema },
      { name: ManageAccount.name, schema: ManageAccountSchema },
    ]),
    DashboardModule,
    NotificationsModule,
  ],
  controllers: [BrokerController],
  providers: [BrokerService, MailService],
  exports: [BrokerService],
})
export class BrokerModule {}
