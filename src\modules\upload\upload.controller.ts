import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Body,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UploadService } from './upload.service';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';

@ApiTags('File Upload')
@Controller('upload')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('single')
  @ApiOperation({ summary: 'Upload single file to Google Cloud Storage' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ 
    status: 200, 
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: { type: 'string', example: 'File uploaded successfully' },
        data: {
          type: 'object',
          properties: {
            fileName: { type: 'string', example: 'brokers/image_1234567890_abc123.jpg' },
            publicUrl: { type: 'string', example: 'https://storage.googleapis.com/trade-reward-bucket/brokers/image_1234567890_abc123.jpg' },
            gsUrl: { type: 'string', example: 'gs://trade-reward-bucket/brokers/image_1234567890_abc123.jpg' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid file or file type not allowed' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingleFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('folder') folder?: string,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const result = await this.uploadService.uploadFile(file, folder);

    return {
      status: 200,
      message: 'File uploaded successfully',
      data: result,
    };
  }

  @Post('multiple')
  @ApiOperation({ summary: 'Upload multiple files to Google Cloud Storage' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ 
    status: 200, 
    description: 'Files uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Files uploaded successfully' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              fileName: { type: 'string' },
              publicUrl: { type: 'string' },
              gsUrl: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid files or file types not allowed' })
  @UseInterceptors(FilesInterceptor('files', 10)) // Max 10 files
  async uploadMultipleFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Body('folder') folder?: string,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    const results = await this.uploadService.uploadMultipleFiles(files, folder);

    return {
      status: 200,
      message: 'Files uploaded successfully',
      data: results,
    };
  }

  @Post('broker-image')
  @ApiOperation({ summary: 'Upload broker image' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: 'Broker image uploaded successfully' })
  @UseInterceptors(FileInterceptor('image'))
  async uploadBrokerImage(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No image uploaded');
    }

    // Validate that it's an image
    if (!file.mimetype.startsWith('image/')) {
      throw new BadRequestException('Only image files are allowed');
    }

    const result = await this.uploadService.uploadFile(file, 'brokers');

    return {
      status: 200,
      message: 'Broker image uploaded successfully',
      data: result,
    };
  }
}
