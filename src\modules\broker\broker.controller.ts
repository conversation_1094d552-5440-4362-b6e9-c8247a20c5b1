import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  HttpCode,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { BrokerService } from './broker.service';
import {
  CreateBrokerDto,
  UpdateBrokerDto,
  RateBrokerDto,
  SendEmailDto,
} from './dto/broker.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { Public } from '../../Gaurd/public.decorator';

@ApiTags('Broker')
@Controller('broker')
@UseGuards(JwtAuthGuard)
export class BrokerController {
  constructor(private readonly brokerService: BrokerService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create a new broker' })
  @ApiResponse({ status: 201, description: 'Broker created successfully' })
  @ApiResponse({
    status: 409,
    description: 'Broker with this name already exists',
  })
  @HttpCode(HttpStatus.CREATED)
  createBroker(@Body() createBrokerDto: CreateBrokerDto, @Request() req: any) {
    const userId = req.user.userId;
    return this.brokerService.create(createBrokerDto, userId);
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all brokers (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of all brokers' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Admin access required' })
  getAllBrokers(@Request() req: any) {
    const userId = req.user.userId;
    return this.brokerService.findAll(userId);
  }

  @Get('get-all-brokers')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all brokers (Public)' })
  @ApiResponse({ status: 200, description: 'List of all brokers' })
  getAllBrokersPublic(@Request() req: any) {
    const userId = req.user.userId;
    return this.brokerService.findAllPublic();
  }

  @Get('topLeaderBoardBrokers')
  @Public()
  @ApiOperation({ summary: 'Get top 4 brokers by connected users count' })
  @ApiResponse({
    status: 200,
    description: 'Top 4 brokers retrieved successfully',
  })
  async getTopLeaderBoardbroker() {
    const top4Brokers = await this.brokerService.getTopLeaderBoardBrokers();
    return {
      status: 200,
      message: 'Top 4 brokers retrieved successfully',
      data: top4Brokers,
    };
  }

  @Get('connected')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get brokers connected to current user' })
  @ApiResponse({
    status: 200,
    description: 'Connected brokers retrieved successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getConnectedBrokers(@Request() req: any) {
    const userId = req.user.userId;
    console.log('i am userID:', userId);

    const connectedBrokers = await this.brokerService.getConnectedBrokers(
      userId.toString(),
    );
    return {
      status: 200,
      message: 'Connected brokers retrieved successfully',
      data: connectedBrokers,
    };
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get broker by ID' })
  @ApiResponse({ status: 200, description: 'Broker found' })
  @ApiResponse({ status: 404, description: 'Broker not found' })
  getBrokerById(@Param('id') id: string) {
    return this.brokerService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Update broker' })
  @ApiResponse({ status: 200, description: 'Broker updated successfully' })
  @ApiResponse({ status: 404, description: 'Broker not found' })
  @ApiResponse({
    status: 409,
    description: 'Broker with this name already exists',
  })
  updateBroker(
    @Param('id') id: string,
    @Body() updateBrokerDto: UpdateBrokerDto,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    return this.brokerService.update(id, updateBrokerDto, userId);
  }

  @Delete(':id')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Delete broker' })
  @ApiResponse({ status: 200, description: 'Broker deleted successfully' })
  @ApiResponse({ status: 404, description: 'Broker not found' })
  deleteBroker(@Param('id') id: string) {
    return this.brokerService.remove(id);
  }

  @Post(':id/rate')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Rate a broker' })
  @ApiResponse({
    status: 200,
    description: 'Broker rated successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Broker rated successfully' },
        data: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            brokerName: { type: 'string' },
            ratings: { type: 'number', example: 4.2 },
            totalRatings: { type: 'number', example: 15 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Broker not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.OK)
  async rateBroker(
    @Param('id') id: string,
    @Body() rateBrokerDto: RateBrokerDto,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    const result = await this.brokerService.rateBroker(
      id,
      userId,
      rateBrokerDto,
      
    );
    return {
      status: 200,
      message: 'Broker rated successfully',
      data: {
        _id: result._id,
        brokerName: result.brokerName,
        ratings: result.ratings,
        totalRatings: result.totalRatings,
        userRatings: result.userRatings,
        
      },
    };
  }

  @Post('send-email')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Send custom email using SendGrid' })
  @ApiResponse({
    status: 200,
    description: 'Email sent successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Email sent successfully' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 500, description: 'Failed to send email' })
  @HttpCode(HttpStatus.OK)
  async sendEmail(@Body() sendEmailDto: SendEmailDto, @Request() req: any) {
    const userId = req.user.userId;
    const result = await this.brokerService.sendEmail(sendEmailDto, userId);
    return {
      status: 200,
      message: result.message,
    };
  }

  @Delete(':id/rating')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Delete user rating for a broker' })
  @ApiResponse({
    status: 200,
    description: 'Rating deleted successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Rating deleted successfully' },
        data: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            brokerName: { type: 'string' },
            ratings: { type: 'number', example: 4.2 },
            totalRatings: { type: 'number', example: 14 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Broker not found or User rating not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.OK)
  async deleteUserRating(
    @Param('id') id: string,
    @Body() body: { userId: string },
    @Request() req: any,
  ) {
    const adminId = req.user.userId;
    
    const result = await this.brokerService.deleteUserRating(id, body.userId,adminId);
    return {
      status: 200,
      message: 'Rating deleted successfully',
      data: {
        _id: result._id,
        brokerName: result.brokerName,
        ratings: result.ratings,
        totalRatings: result.totalRatings,
        userRatings: result.userRatings,
      },
    };
  }

  @Patch(':id/featured')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Toggle broker featured status (Admin only)' })
  @ApiResponse({ status: 200, description: 'Broker featured status updated successfully' })
  @ApiResponse({ status: 404, description: 'Broker not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Admin access required' })
  async toggleFeaturedStatus(
    @Param('id') id: string,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    const result = await this.brokerService.toggleFeaturedStatus(id, userId);
    return {
      status: 200,
      message: 'Broker featured status updated successfully',
      data: result,
    };
  }
}
