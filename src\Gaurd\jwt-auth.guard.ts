import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      console.log('🔓 Public route accessed, skipping JWT validation');
      return true;
    }

    console.log('🔒 Protected route accessed, validating JWT token');
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      console.log('❌ JWT Authentication failed:', err?.message || info?.message || 'Unknown error');
      throw err || new UnauthorizedException('Invalid or missing token');
    }

    console.log('✅ JWT Authentication successful for user:', user.email);
    return user;
  }
}
