import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Server } from 'socket.io';
import { Notification } from './entities/notification.entity';
import { CreateNotificationDto } from './dto/notification.dto';

@Injectable()
export class NotificationsService {
  private io: Server;

  constructor(
    @InjectModel(Notification.name)
    private notificationModel: Model<Notification>,
  ) {}

  setIo(io: Server) {
    this.io = io;
  }

  async create(createNotificationDto: CreateNotificationDto) {
    const notification = new this.notificationModel(createNotificationDto);
    const savedNotification = await notification.save();
    
    // Emit real-time notification
    if (this.io) {
      this.io.to(`user_${createNotificationDto.userId}`).emit('newNotification', savedNotification);
    }
    
    return savedNotification;
  }

  async getAllUserNotifications(userId: string) {
    return await this.notificationModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .exec();
  }

  async getById(id: string) {
    const notification = await this.notificationModel.findById(id);
    if (!notification) {
      throw new NotFoundException('Notification not found');
    }
    
    // Mark as read when getting by ID
    notification.isRead = true;
    const updatedNotification = await notification.save();
    
    // Emit real-time update for read status
    if (this.io) {
      this.io.to(`user_${notification.userId}`).emit('notificationRead', {
        notificationId: id,
        isRead: true,
        notification: updatedNotification
      });
    }
    
    return updatedNotification;
  }

  async markAllAsRead(userId: string) {
    const result = await this.notificationModel.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );
 if (this.io) {
    this.io.to(`user_${userId}`).emit('allNotificationsRead', {
      userId,
      modifiedCount: result.modifiedCount
    });
  }


    return result;
  }

  async getUnreadCount(userId: string) {
    return await this.notificationModel.countDocuments({
      userId,
      isRead: false
    });
  }

  // Helper methods for creating specific notification types
  async createWithdrawalNotification(userId: string, type: 'success' | 'rejected', amount: number, withdrawalId: string) {
    const title = type === 'success' ? 'Withdrawal Successful' : 'Withdrawal Rejected';
    const message = type === 'success' 
      ? `Your withdrawal of $${amount} has been processed successfully.`
      : `Your withdrawal request of $${amount} has been rejected.`;

    return this.create({
      userId,
      title,
      message,
      type: type === 'success' ? 'withdrawal_success' : 'withdrawal_rejected',
      metadata: { amount, withdrawalId }
    });
  }

  async createReferralNotification(userId: string, level: number, amount: number, referralUserId: string) {
    const title = amount > 0 
      ? `Level ${level} Referral Earning`
      : `New Level ${level} Referral`;
    
    const message = amount > 0
      ? `You earned $${amount} from your level ${level} referral.`
      : `You have a new level ${level} referral in your network.`;

    return this.create({
      userId,
      title,
      message,
      type: `referral_level${level}` as any,
      metadata: { amount, level, referralUserId }
    });
  }

  async createBrokerNotification(userId: string, brokerName: string, brokerId: string) {
    return this.create({
      userId,
      title: 'New Broker Added',
      message: `New broker "${brokerName}" has been added to the platform.`,
      type: 'new_broker',
      metadata: { brokerId }
    });
  }

  async createRebateNotification(userId: string, amount: number) {
    return this.create({
      userId,
      title: 'Rebate Received',
      message: `You received a rebate of $${amount}.`,
      type: 'rebate',
      metadata: { amount }
    });
  }

  async createAccountStatusNotification(userId: string, status: 'locked' | 'unlocked') {
    const title = status === 'locked' ? 'Account Locked' : 'Account Unlocked';
    const message = status === 'locked' 
      ? 'Your account has been locked by admin.'
      : 'Your account has been unlocked by admin.';

    return this.create({
      userId,
      title,
      message,
      type: status === 'locked' ? 'account_locked' : 'account_unlocked',
      metadata: {}
    });
  }

  async createCashbackNotification(userId: string, amount: number) {
    return this.create({
      userId,
      title: 'Cashback Received',
      message: `You received a cashback of $${amount}.`,
      type: 'cashback',
      metadata: { cashbackAmount: amount }
    });
  }
}
