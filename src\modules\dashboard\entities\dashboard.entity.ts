import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Dashboard extends Document {
  // Cumulative totals
  @Prop({ default: 0 })
  totalBrokers: number;

  @Prop({ default: 0 })
  totalWithdrawals: number;

  @Prop({ default: 0 })
  totalBrokerRebate: number;

  // Today's values
  @Prop({ default: 0 })
  todayBrokers: number;

  @Prop({ default: 0 })
  todayWithdrawals: number;

  @Prop({ default: 0 })
  todayBrokerRebate: number;

  // Yesterday's values
  @Prop({ default: 0 })
  yesterdayBrokers: number;

  @Prop({ default: 0 })
  yesterdayWithdrawals: number;

  @Prop({ default: 0 })
  yesterdayBrokerRebate: number;

  @Prop({ type: Date, default: Date.now })
  lastUpdated: Date;
}

export const DashboardSchema = SchemaFactory.createForClass(Dashboard);
