import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateSupportDto, AdminReplyDto } from './dto/create-support.dto';
import { Support } from './entities/support.entity';
import { Auth } from '../auth/entities/auth.entity';

@Injectable()
export class SupportService {
  constructor(
    @InjectModel(Support.name) private readonly supportModel: Model<Support>,
    @InjectModel(Auth.name) private readonly authModel: Model<Auth>,
  ) {}

  async createSupport(createSupportDto: CreateSupportDto, userId: string) {
    try {
      const created = new this.supportModel({ ...createSupportDto, userId });
      return await created.save();
    } catch (error) {
      throw new HttpException(
        `Failed to create support ticket: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async findAll() {
    try {
      return await this.supportModel.find().sort({ createdAt: -1 }).exec();
    } catch (error) {
      throw new HttpException(
        `Failed to fetch support tickets: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: string) {
    try {
      const result = await this.supportModel.findById(id).exec();
      if (!result) {
        throw new HttpException(
          'Support ticket not found',
          HttpStatus.NOT_FOUND,
        );
      }
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch support ticket: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async replyToSupport(
    id: string,
    adminReplyDto: AdminReplyDto,
    adminId: string,
  ) {
    try {
      const user = await this.authModel.findById(adminId).exec();
      if (!user) {
        throw new HttpException('Admin not found', HttpStatus.NOT_FOUND);
      }

      if (user.role !== 'admin') {
        throw new HttpException(
          'You are not authorized to reply to support tickets',
          HttpStatus.FORBIDDEN,
        );
      }

      const supportTicket = await this.supportModel.findById(id).exec();
      if (!supportTicket) {
        throw new HttpException(
          'Support ticket not found',
          HttpStatus.NOT_FOUND,
        );
      }

      supportTicket.status = 'active';
      supportTicket.isReplied = true;
      supportTicket.repliedAt = new Date();

      const updatedTicket = await supportTicket.save();

      return updatedTicket;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to reply to support ticket: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkAdminAccess(adminId: string) {
    try {
      const user = await this.authModel.findById(adminId).exec();
      if (!user) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }

      if (user.role !== 'admin') {
        throw new HttpException(
          'Access denied. Admin privileges required.',
          HttpStatus.FORBIDDEN,
        );
      }

      return user;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to verify admin access: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findSupportsByUserId(userId: string) {
    try {
      return await this.supportModel
        .find({ userId })
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      throw new HttpException(
        `Failed to fetch support tickets for user: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async closeSupport(id: string, adminId: string) {
    try {
      const user = await this.authModel.findById(adminId).exec();
      if (!user) {
        throw new HttpException('Admin not found', HttpStatus.NOT_FOUND);
      }

      if (user.role !== 'admin') {
        throw new HttpException(
          'You are not authorized to close support tickets',
          HttpStatus.FORBIDDEN,
        );
      }

      const ticket = await this.supportModel.findById(id).exec();
      if (!ticket) {
        throw new HttpException(
          'Support ticket not found',
          HttpStatus.NOT_FOUND,
        );
      }

      ticket.status = 'closed';
      const updatedTicket = await ticket.save();

      return updatedTicket;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to close support ticket: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
