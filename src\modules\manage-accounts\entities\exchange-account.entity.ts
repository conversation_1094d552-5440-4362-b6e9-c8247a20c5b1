import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class ExchangeAccount extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Auth', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Exchange', required: true })
  exchangeId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  accountNo: string;

  @Prop({ required: true })
  accountType: string;

  @Prop({ default: 'pending' })
  status: string;

   @Prop({ default: 0 })
  totalProfit: number;

  @Prop({ default: '' })
  firstname: string;

  @Prop({ default: '' })
  lastname: string;
}

export const ExchangeAccountSchema = SchemaFactory.createForClass(ExchangeAccount);
