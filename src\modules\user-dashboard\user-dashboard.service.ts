import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserDashboard } from './entities/user-dashboard.entity';
import { UserEarnings } from './entities/user-earnings.entity';
import { Auth } from '../auth/entities/auth.entity';

@Injectable()
export class UserDashboardService {
  constructor(
    @InjectModel(UserDashboard.name)
    private userDashboardModel: Model<UserDashboard>,
    @InjectModel(UserEarnings.name)
    private userEarningsModel: Model<UserEarnings>,
    @InjectModel(Auth.name)
    private authModel: Model<Auth>,
  ) {}

  async getUserRank(userId: string): Promise<{ rank: string; userId: string; requirement: number ; totalProfit: number; currentEarnings: number }> {
    const user = await this.authModel.findById(userId).select('rank totalTradeEarnings').exec();

     if (!user) {
      throw new Error('User not found');
    }

    let requirement;
    if(user.rank == 'bronze'){
      requirement = 5000;
    }
    if(user.rank == 'silver'){
      requirement = 10000;
    }
    if(user.rank == 'gold'){
      requirement = 10000;
    }
    // console.log("user",user);
    
    
   
    return {
      userId,
      rank: user.rank,
      requirement: requirement,
      totalProfit:user.totalTradeEarnings,
      currentEarnings:user.totalTradeEarnings ||0
    };
  }

  async getUserMonthlyDashboard(userId: string): Promise<UserDashboard[]> {
    return this.userDashboardModel
      .find({ userId })
      .sort({ year: -1, month: -1 })
      .exec();
  }

  async getCurrentMonthDashboard(userId: string): Promise<UserDashboard> {
    const now = new Date();
    const currentMonth = now.getUTCMonth() + 1; // 1-12
    const currentYear = now.getUTCFullYear();

    let dashboard = await this.userDashboardModel.findOne({
      userId,
      month: currentMonth,
      year: currentYear,
    });

    if (!dashboard) {
      // Create new dashboard for current month
      dashboard = new this.userDashboardModel({
        userId,
        month: currentMonth,
        year: currentYear,
      });
      
      await dashboard.save();
    }

    return dashboard;
  }

  async updateUserDashboard(userId: string, updateData: Partial<UserDashboard>): Promise<UserDashboard> {
    const dashboard = await this.getCurrentMonthDashboard(userId);
    
    // Update dashboard with new data
    Object.keys(updateData).forEach(key => {
      if (dashboard[key] !== undefined && key !== 'userId' && key !== 'month' && key !== 'year') {
        dashboard[key] = updateData[key];
      }
    });
    
    return dashboard.save();
  }

  async updateUserBalance(userId: string, newBalance: number): Promise<UserDashboard> {
    const dashboard = await this.getCurrentMonthDashboard(userId);
    dashboard.availableBalance = newBalance;
    return dashboard.save();
  }

  async addRebate(userId: string, amount: number): Promise<UserDashboard> {
    const dashboard = await this.getCurrentMonthDashboard(userId);
    dashboard.totalRebates += amount;
    return dashboard.save();
  }

  async addTradeProfit(userId: string, amount: number): Promise<UserDashboard> {
    const dashboard = await this.getCurrentMonthDashboard(userId);
    dashboard.totalTradeProfit += amount;
    
    // Add to daily earnings
    await this.addDailyEarning(userId, { totalTradeProfit: amount });
    
    return dashboard.save();
  }

  async addReferralEarning(userId: string, amount: number, level: number): Promise<UserDashboard> {
    const dashboard = await this.getCurrentMonthDashboard(userId);
    
    const earningUpdate: any = {};
    
    if (level === 1) {
      dashboard.level1ReferralEarnings += amount;
      earningUpdate.level1ReferralEarnings = amount;
    } else if (level === 2) {
      dashboard.level2ReferralEarnings += amount;
      earningUpdate.level2ReferralEarnings = amount;
    } else if (level === 3) {
      dashboard.level3ReferralEarnings += amount;
      earningUpdate.level3ReferralEarnings = amount;
    }
    
    // Add to daily earnings
    await this.addDailyEarning(userId, earningUpdate);
    
    return dashboard.save();
  }

  private async addDailyEarning(userId: string, earnings: {
    totalTradeProfit?: number;
    level1ReferralEarnings?: number;
    level2ReferralEarnings?: number;
    level3ReferralEarnings?: number;
  }): Promise<UserEarnings> {
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0); // Start of day

    let dailyEarning = await this.userEarningsModel.findOne({
      userId,
      date: today,
    });

    if (!dailyEarning) {
      dailyEarning = new this.userEarningsModel({
        userId,
        date: today,
        totalTradeProfit: 0,
        level1ReferralEarnings: 0,
        level2ReferralEarnings: 0,
        level3ReferralEarnings: 0,
        totalDailyEarnings: 0,
      });
    }

    // Update earnings
    if (earnings.totalTradeProfit) {
      dailyEarning.totalTradeProfit += earnings.totalTradeProfit;
    }
    if (earnings.level1ReferralEarnings) {
      dailyEarning.level1ReferralEarnings += earnings.level1ReferralEarnings;
    }
    if (earnings.level2ReferralEarnings) {
      dailyEarning.level2ReferralEarnings += earnings.level2ReferralEarnings;
    }
    if (earnings.level3ReferralEarnings) {
      dailyEarning.level3ReferralEarnings += earnings.level3ReferralEarnings;
    }

    // Calculate total daily earnings
    dailyEarning.totalDailyEarnings = 
      dailyEarning.totalTradeProfit + 
      dailyEarning.level1ReferralEarnings + 
      dailyEarning.level2ReferralEarnings + 
      dailyEarning.level3ReferralEarnings;

    return dailyEarning.save();
  }

  async getUserEarningsHistory(userId: string, days: number = 30): Promise<UserEarnings[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setUTCHours(0, 0, 0, 0);

    return this.userEarningsModel
      .find({
        userId,
        date: { $gte: startDate },
      })
      .sort({ date: 1 })
      .exec();
  }

  async getUserEarningsByDateRange(userId: string, startDate: string, endDate: string): Promise<UserEarnings[]> {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    start.setUTCHours(0, 0, 0, 0);
    end.setUTCHours(23, 59, 59, 999);

    return this.userEarningsModel
      .find({
        userId,
        date: { $gte: start, $lte: end },
      })
      .sort({ date: 1 })
      .exec();
  }
}




