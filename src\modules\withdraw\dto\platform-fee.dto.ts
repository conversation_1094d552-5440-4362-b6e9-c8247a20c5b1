import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AddPlatformFeeDto {
  @ApiProperty({ description: 'ETH withdrawal fee', example: 0.005, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'ETH fee must be non-negative' })
  ethFee?: number;

  @ApiProperty({ description: 'SOL withdrawal fee', example: 0.01, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'SOL fee must be non-negative' })
  solFee?: number;

  @ApiProperty({ description: 'BTC withdrawal fee', example: 0.0001, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'BTC fee must be non-negative' })
  btcFee?: number;
}
