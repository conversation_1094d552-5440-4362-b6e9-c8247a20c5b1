import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class IncentiveUserDto {
  @ApiProperty({ description: 'User ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'User Name' })
  @IsString()
  userName: string;

  @ApiProperty({ description: 'Cashback amount' })
  @IsNumber()
  cashback: number;
}

export class CreateIncentiveDto {
  @ApiProperty({
    description: 'Array of users to give incentives to',
    type: [IncentiveUserDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IncentiveUserDto)
  users: IncentiveUserDto[];

  @ApiProperty({
    description: 'Description for the incentive',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
