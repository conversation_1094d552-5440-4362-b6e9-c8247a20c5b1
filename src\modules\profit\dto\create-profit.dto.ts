import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON>teNested, IsString, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class ProcessCsvDto {
  @ApiProperty({
    description: 'CSV file content as multipart/form-data',
    type: 'string',
    format: 'binary'
  })
  csvFile: any;

  @ApiProperty({
    description: 'Broker ID for which the CSV is being processed',
    type: 'string',
    example: '507f1f77bcf86cd799439011'
  })
  brokerId: string;
}

export class CsvRowData {
  @ApiProperty({ description: 'Trading account number', example: '********' })
  @IsString()
  tradingAccount: string;
  
 @IsOptional()
  @IsArray()
  csvData?: Array<{
    title: string;
    content: string;
  }>;
  

  // @ApiProperty({ description: 'Account holder name', example: '<PERSON>' })
  // @IsString()
  // name: string;

  // @ApiProperty({ description: 'Account type', example: 'Standard' })
  // @IsString()
  // accountType: string;

  // @ApiProperty({ description: 'Node name', example: 'Node1' })
  // @IsString()
  // nodeName: string;

  // @ApiProperty({ description: 'Trading platform', example: 'MT4' })
  // @IsString()
  // platform: string;

  // @ApiProperty({ description: 'Rebate method', example: 'Fixed' })
  // @IsString()
  // rebateMethod: string;

  // @ApiProperty({ description: 'IB currency', example: 'USDT' })
  // @IsString()
  // ibCurrency: string;

  @ApiProperty({ description: 'Rebate amount', example: 100.50 })
  @IsNumber()
  rebate: number;

  // @ApiProperty({ description: 'FOREX trading volume', example: 50.25, required: false })
  // @IsOptional()
  // @IsNumber()
  // FOREX?: number;

  // @ApiProperty({ description: 'BULLION trading volume', example: 25.10, required: false })
  // @IsOptional()
  // @IsNumber()
  // BULLION?: number;

  // @ApiProperty({ description: 'OIL trading volume', example: 15.75, required: false })
  // @IsOptional()
  // @IsNumber()
  // OIL?: number;

  // @ApiProperty({ description: 'CFD trading volume', example: 10.50, required: false })
  // @IsOptional()
  // @IsNumber()
  // CFD?: number;

  // @ApiProperty({ description: 'CRYPTO trading volume', example: 20.30, required: false })
  // @IsOptional()
  // @IsNumber()
  // CRYPTO?: number;

  // @ApiProperty({ description: 'SHARES trading volume', example: 5.25, required: false })
  // @IsOptional()
  // @IsNumber()
  // SHARES?: number;

  // @ApiProperty({ description: 'BTCUSD trading volume', example: 8.15, required: false })
  // @IsOptional()
  // @IsNumber()
  // BTCUSD?: number;
}

export class ProcessArrayDto {
  @ApiProperty({
    description: 'Array of trading data objects',
    type: [CsvRowData]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CsvRowData)
  data: CsvRowData[];

  @ApiProperty({
    description: 'Start date for the CSV data period',
    example: '2023-01-01'
  })
  @IsString()
  startDate: string;

  @ApiProperty({
    description: 'End date for the CSV data period',
    example: '2023-01-31'
  })
  @IsString()
  endDate: string;

  @ApiProperty({
    description: 'CSV data array with title and content',
    type: 'array',
    required: false
  })
  @IsOptional()
  @IsArray()
  csvData?: Array<{
    title: string;
    content: string;
  }>;
}
