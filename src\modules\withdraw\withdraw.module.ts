import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WithdrawService } from './withdraw.service';
import { WithdrawController } from './withdraw.controller';
import { PlatformFeeService } from './platform-fee.service';
import { PlatformFeeController } from './platform-fee.controller';
import { Withdraw, WithdrawSchema } from './entities/withdraw.entity';
import { PlatformFee, PlatformFeeSchema } from './entities/platfomFee';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';
import {
  CreditDebitHistory,
  CreditDebitHistorySchema,
} from '../profit/entities/credit-debit-history';
import { DashboardModule } from '../dashboard/dashboard.module';
import { UserDashboardModule } from '../user-dashboard/user-dashboard.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Withdraw.name, schema: WithdrawSchema },
      { name: PlatformFee.name, schema: PlatformFeeSchema },
      { name: Auth.name, schema: AuthSchema },
      { name: CreditDebitHistory.name, schema: CreditDebitHistorySchema },
    ]),
    DashboardModule,
    UserDashboardModule,
    NotificationsModule,
  ],
  controllers: [WithdrawController, PlatformFeeController],
  providers: [WithdrawService, PlatformFeeService],
})
export class WithdrawModule {}
