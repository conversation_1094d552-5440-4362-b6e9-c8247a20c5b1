import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class CreditDebitHistory extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, enum: ['credit', 'debit'] })
  type: string;

  @Prop({ default: 0 })
  amount: number;

  @Prop({ default: '' })
  firstname: string;

  @Prop({ default: '' })
  lastname: string;

  @Prop({ default: '' })
  accountNo: string;

  @Prop({
    default: '',
    enum: ['referral', 'trade', 'withdraw', 'admin', 'incentive'],
  })
  source: string;

  @Prop({ default: 'usdt' })
  currency: string;

  @Prop({ default: '' })
  description: string;

  @Prop({ default: 0 })
  balanceAfter: number;

  @Prop({ default: 0 })
  balanceBefore: number;

  @Prop({ default: '' })
  from: string; // Admin wallet/name for withdraw, referrer name for referral, "admin" for incentive

  @Prop({ default: '' })
  to: string; // User wallet for withdraw, referred user name for referral, user name for incentive
}

export const CreditDebitHistorySchema =
  SchemaFactory.createForClass(CreditDebitHistory);
