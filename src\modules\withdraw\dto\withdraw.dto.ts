import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsString, Min, IsEnum, IsOptional } from 'class-validator';

export class CreateWithdrawDto {
  @IsNotEmpty()
  @IsNumber()
  @Min(50, { message: 'Minimum withdrawal amount is 50' })
  amount: number;

  @IsNotEmpty()
  @IsString()
  walletAddress: string;

  @IsNotEmpty()
  @IsString()
  withdrawCurrency: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Fee must be non-negative' })
  fee: number;

  @IsNotEmpty()
  @IsEnum(['eth', 'sol', 'bsc'], { message: 'Withdraw method must be eth, sol or bsc' })
  withdrawMethod: string;
}

export class UpdateWithdrawStatusDto {
  @IsNotEmpty()
  @IsEnum(['completed', 'rejected'], { message: 'Status must be either completed or rejected' })
  status: string;

  @IsOptional()
  @IsString()
  rejectionReason?: string;
}
