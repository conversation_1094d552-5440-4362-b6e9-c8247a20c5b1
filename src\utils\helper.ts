// helpers/referral.helper.ts
import { ethers } from 'ethers';

export async function generateReferrals(currentUser: any, referredUser: any, referralTreeModel?: any, notificationsService?: any): Promise<void> {
  let currentReferrer = referredUser;

  for (let level = 1; level <= 3; level++) {
    if (!currentReferrer) {
      break;
    }

    if (level === 1) {
      currentReferrer.level1.push(currentUser._id.toString());
    } else if (level === 2) {
      currentReferrer.level2.push(currentUser._id.toString());
    } else if (level === 3) {
      currentReferrer.level3.push(currentUser._id.toString());
    }

    await currentReferrer.save();

    // Create referral notification for the referrer
    if (notificationsService) {
      try {
        await notificationsService.createReferralNotification(
          currentReferrer._id.toString(),
          level,
          0, // Amount will be updated when they earn from this referral
          currentUser._id.toString()
        );
        console.log(`✅ Referral notification created for level ${level} referrer:`, currentReferrer.email);
      } catch (error) {
        console.error(`❌ Failed to create referral notification for level ${level}:`, error);
      }
    }

    if (currentReferrer.refferedBy) {
      const nextReferrer = await currentReferrer.constructor.findOne({
        referralCode: currentReferrer.refferedBy
      });
      currentReferrer = nextReferrer;
    } else {
      break;
    }
  }

  if (referralTreeModel) {
    await calculateReferralTree(currentUser, referredUser, referralTreeModel);
  }
}

export async function calculateReferralTree(currentUser: any, referralUser: any, referralTreeModel: any) {
  const referralTree: any = {
    userId: currentUser._id.toString(),
    userEmail: currentUser.email,
    userName: `${currentUser.firstName} ${currentUser.lastName}`,
    referralChain: [],
    totalLevels: 0,
    rootReferrer: null
  };

  let currentReferrer = referralUser;
  let level = 1;

  
  if (currentReferrer) {
    referralTree.referralChain.push({
      level: level,
      userId: currentReferrer._id.toString(),
      userEmail: currentReferrer.email,
      userName: `${currentReferrer.firstName} ${currentReferrer.lastName}`,
      referralCode: currentReferrer.referralCode,
      referredBy: currentReferrer.refferedBy || null
    });

    
    if (!currentReferrer.refferedBy) {
      referralTree.rootReferrer = {
        userId: currentReferrer._id.toString(),
        userEmail: currentReferrer.email,
        userName: `${currentReferrer.firstName} ${currentReferrer.lastName}`,
        referralCode: currentReferrer.referralCode,
        level: level
      };
    } else {
      
      level++;

      while (currentReferrer.refferedBy) {
        try {
          const nextReferrer = await currentReferrer.constructor.findOne({
            referralCode: currentReferrer.refferedBy
          });

          if (!nextReferrer) {
            
            break;
          }

          // Add next referrer to the chain
          referralTree.referralChain.push({
            level: level,
            userId: nextReferrer._id.toString(),
            userEmail: nextReferrer.email,
            userName: `${nextReferrer.firstName} ${nextReferrer.lastName}`,
            referralCode: nextReferrer.referralCode,
            referredBy: nextReferrer.refferedBy || null
          });

          // If this referrer has no one who referred them, they are the root
          if (!nextReferrer.refferedBy) {
            referralTree.rootReferrer = {
              userId: nextReferrer._id.toString(),
              userEmail: nextReferrer.email,
              userName: `${nextReferrer.firstName} ${nextReferrer.lastName}`,
              referralCode: nextReferrer.referralCode,
              level: level
            };
            break;
          }

          currentReferrer = nextReferrer;
          level++;

        } catch (error) {
          console.error('Error finding next referrer:', error);
          break;
        }
      }
    }
  }

  referralTree.totalLevels = level - 1;

  // Save referral tree to database
  try {
    // Check if referral tree already exists for this user
    const existingTree = await referralTreeModel.findOne({ userId: currentUser._id.toString() });

    if (existingTree) {
      // Update existing tree
      await referralTreeModel.findByIdAndUpdate(existingTree._id, referralTree);
      console.log('✅ Referral tree updated for user:', currentUser.email);
    } else {
      // Create new tree
      const newTree = new referralTreeModel(referralTree);
      await newTree.save();
      console.log('✅ Referral tree created for user:', currentUser.email);
    }
  } catch (error) {
    console.error('❌ Error saving referral tree:', error);
  }

  return referralTree;
}

// AirDrop Contract ABI - Only the functions we need
const AIRDROP_ABI = [
  "function airDrop(address[] calldata _recipients, uint256[] calldata _amount) external",
  "function usdtToken() external view returns (address)",
  "function FundsWallet() external view returns (address)",
  "function owner() external view returns (address)"
];

// ERC20 Token ABI for token operations
const ERC20_ABI = [
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function balanceOf(address account) external view returns (uint256)"
];

// AirDrop function to transfer tokens to multiple users
export async function executeAirDrop(recipients: string[], amounts: string[]): Promise<{ success: boolean; message: string; txHash?: string }> {
  try {
    // Validate inputs
    if (!recipients || !amounts || recipients.length === 0 || amounts.length === 0) {
      throw new Error('Recipients and amounts arrays cannot be empty');
    }

    if (recipients.length !== amounts.length) {
      throw new Error('Recipients and amounts arrays must have the same length');
    }

    // Environment variables
    const privateKey = process.env.META_PRIVATE_KEY;
    const walletAddress = process.env.META_WALLET_ADDRESS;
    const contractAddress = '******************************************';

    if (!privateKey || !walletAddress) {
      throw new Error('META_PRIVATE_KEY and META_WALLET_ADDRESS must be set in environment variables');
    }

    // Setup provider and wallet for Sepolia testnet - Try multiple RPC endpoints
    const rpcEndpoints = [
      'https://ethereum-sepolia-rpc.publicnode.com',
      'https://sepolia.gateway.tenderly.co',
      'https://rpc2.sepolia.org',
      'https://rpc.sepolia.org',
      'https://ethereum-sepolia.blockpi.network/v1/rpc/public'
    ];

    let provider: ethers.JsonRpcProvider | null = null;

    // Try different RPC endpoints until one works
    for (const rpcUrl of rpcEndpoints) {
      try {
        console.log(`🔄 Trying RPC endpoint: ${rpcUrl}`);
        const testProvider = new ethers.JsonRpcProvider(rpcUrl);

        // Test connection by getting network info
        await testProvider.getNetwork();
        console.log(`✅ Connected successfully to: ${rpcUrl}`);
        provider = testProvider;
        break;
      } catch (error) {
        console.log(`❌ Failed to connect to: ${rpcUrl}`);
        continue;
      }
    }

    if (!provider) {
      throw new Error('Failed to connect to any Sepolia RPC endpoint. Please try again later.');
    }
    const wallet = new ethers.Wallet(privateKey, provider);

    // Create contract instance
    const contract = new ethers.Contract(contractAddress, AIRDROP_ABI, wallet);

    // Convert amounts to Wei (assuming amounts are in USDT with 6 decimals)
    const amountsInWei = amounts.map(amount => ethers.parseUnits(amount, 6)); // USDT has 6 decimals

    // Calculate total amount needed
    const totalAmount = amountsInWei.reduce((sum, amount) => sum + amount, BigInt(0));

    console.log('🚀 Starting AirDrop process...');
    console.log('📊 Recipients:', recipients.length);
    console.log('💰 Total amount:', ethers.formatUnits(totalAmount, 6), 'USDT');

    // Get token address and funds wallet from contract
    const tokenAddress = await contract.usdtToken();
    const fundsWallet = await contract.FundsWallet();
    const contractOwner = await contract.owner();

    console.log('🏦 Token address:', tokenAddress);
    console.log('💼 Funds wallet:', fundsWallet);
    console.log('👤 Contract owner:', contractOwner);

    // Check if our wallet is the owner
    if (walletAddress.toLowerCase() !== contractOwner.toLowerCase()) {
      throw new Error(`Only contract owner can execute airdrops. Owner: ${contractOwner}, Your wallet: ${walletAddress}`);
    }

    // Create token contract instance for balance/allowance checks
    const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, wallet);

    // Check funds wallet balance
    const balance = await tokenContract.balanceOf(fundsWallet);
    console.log('💳 Funds wallet balance:', ethers.formatUnits(balance, 6), 'USDT');

    if (balance < totalAmount) {
      throw new Error(`Insufficient balance in funds wallet. Required: ${ethers.formatUnits(totalAmount, 6)}, Available: ${ethers.formatUnits(balance, 6)}`);
    }

    // Check allowance from funds wallet to contract
    const currentAllowance = await tokenContract.allowance(fundsWallet, contractAddress);
    console.log('🔐 Current allowance:', ethers.formatUnits(currentAllowance, 6), 'USDT');

    if (currentAllowance < totalAmount) {
      throw new Error(`Insufficient allowance. Required: ${ethers.formatUnits(totalAmount, 6)}, Available: ${ethers.formatUnits(currentAllowance, 6)}. Please approve the contract to spend tokens from funds wallet.`);
    }

    // Execute airdrop
    console.log('🔄 Executing airdrop...');
    const transferTx = await contract.airDrop(recipients, amountsInWei);

    console.log('⏳ Waiting for transaction confirmation...');
    const receipt = await transferTx.wait();

    console.log('✅ AirDrop completed successfully!');
    console.log('🔗 Transaction hash:', receipt.hash);
    console.log('⛽ Gas used:', receipt.gasUsed.toString());
    console.log('💰 Total distributed:', ethers.formatUnits(totalAmount, 6), 'USDT');

    return {
      success: true,
      message: `AirDrop completed successfully! Distributed ${ethers.formatUnits(totalAmount, 6)} USDT to ${recipients.length} recipients`,
      txHash: receipt.hash
    };

  } catch (error) {
    console.error('❌ AirDrop failed:', error);
    return {
      success: false,
      message: `AirDrop failed: ${error.message}`
    };
  }
}

// Example usage function for testing
export async function testAirDrop(): Promise<void> {
  // Example data - replace with actual user wallet addresses and amounts
  const recipients = [
    '******************************************', // Example wallet 1
    '******************************************', // Example wallet 2
  ];

  const amounts = [
    '0.1',  // 0.1 USDT to user 1
    '1',  // 0.2 USDT to user 2
  ];

  console.log('🧪 Testing AirDrop function...');
  console.log('📋 Recipients:', recipients);
  console.log('💰 Amounts:', amounts, 'USDT');

  const result = await executeAirDrop(recipients, amounts);

  if (result.success) {
    console.log('✅ Test successful:', result.message);
    console.log('🔗 Transaction hash:', result.txHash);
  } else {
    console.log('❌ Test failed:', result.message);
  }
}
