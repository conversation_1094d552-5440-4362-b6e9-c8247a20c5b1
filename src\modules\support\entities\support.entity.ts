import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';

@Schema({ timestamps: true })
export class Support {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  email: string;

  @Prop({ default: false })
  isReplied: boolean;

  @Prop({ default: 'pending' })
  status: string;

  @Prop({ nullable: true })
  adminReplyTitle: string;

  @Prop({ nullable: true })
  adminReplyMessage: string;

  @Prop({ nullable: true })
  repliedAt: Date;
}

export const SupportSchema = SchemaFactory.createForClass(Support);
