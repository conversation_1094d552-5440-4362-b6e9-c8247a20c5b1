import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateFaqDto, UpdateFaqDto } from './dto/create-faq.dto';
import { Faq } from './entities/faq.entity';

@Injectable()
export class FaqsService {
  constructor(@InjectModel(Faq.name) private faqModel: Model<Faq>) {}

  async createFAQs(createFaqDto: CreateFaqDto): Promise<Faq> {
    try {
      const createdFaq = new this.faqModel(createFaqDto);
      return await createdFaq.save();
    } catch (error) {
      throw new Error(`Failed to create FAQ: ${error.message}`);
    }
  }

  async findAll(): Promise<Faq[]> {
    try {
      return await this.faqModel.find().sort({ createdAt: -1 }).exec();
    } catch (error) {
      throw new Error(`Failed to fetch FAQs: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<Faq> {
    try {
      const faq = await this.faqModel.findById(id).exec();
      if (!faq) {
        throw new NotFoundException(`FAQ with ID ${id} not found`);
      }
      return faq;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch FAQ: ${error.message}`);
    }
  }

  async update(id: string, updateFaqDto: UpdateFaqDto): Promise<Faq> {
    try {
      const updatedFaq = await this.faqModel
        .findByIdAndUpdate(id, updateFaqDto, { new: true })
        .exec();

      if (!updatedFaq) {
        throw new NotFoundException(`FAQ with ID ${id} not found`);
      }

      return updatedFaq;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to update FAQ: ${error.message}`);
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    try {
      const deletedFaq = await this.faqModel.findByIdAndDelete(id).exec();

      if (!deletedFaq) {
        throw new NotFoundException(`FAQ with ID ${id} not found`);
      }

      return { message: `FAQ with ID ${id} has been successfully deleted` };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to delete FAQ: ${error.message}`);
    }
  }
}
