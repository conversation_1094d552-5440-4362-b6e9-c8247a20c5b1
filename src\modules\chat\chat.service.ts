import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Server } from 'socket.io';
import { ChatRoom } from './entities/chat.entity';
import { Message } from './entities/message.entity';
import { Auth } from '../auth/entities/auth.entity';
import { HttpException, HttpStatus } from '@nestjs/common';

@Injectable()
export class LiveChatService {
  private io: Server;

  constructor(
    @InjectModel(ChatRoom.name)
    private readonly chatRoomModel: Model<ChatRoom>,
    @InjectModel(Message.name)
    private readonly messageModel: Model<Message>,
    @InjectModel(Auth.name)
    private readonly authModel: Model<Auth>,
  ) {}

  setIo(io: Server) {
    this.io = io;
    this.setupSocketEvents();
  }

  async createRoom(senderId: string, receiverId: string, requesterId: string) {
    if (!senderId || !receiverId || !requesterId) {
      throw new Error('Sender and Receiver required');
    }

    const userIds = [senderId, receiverId];
    userIds.sort();

    // Check if room already exists with these users
    const room = await this.chatRoomModel.findOne({
      users: { $all: userIds, $size: 2 },
      isGroup: false,
      requesterId: requesterId,
    });

    if (room) {
      return {
        roomId: room.roomId,
        senderId: senderId,
        receiverId: receiverId,
        requesterId: requesterId,
      };
    }

    const newRoom = new this.chatRoomModel({
      roomId: uuidv4(),
      users: userIds,
      isGroup: false,
      requesterId: requesterId,
    });

    const savedRoom = await newRoom.save();

    return {
      roomId: savedRoom.roomId,
      senderId: senderId,
      receiverId: receiverId,
      requesterId: requesterId,
    };
  }

  async sendMessage(
    senderId: string,
    roomId: string,
    receiverId: string,
    requesterId: string,
    messageText: string,
  ) {
    if (!roomId || !senderId || !receiverId || !requesterId || !messageText) {
      throw new Error(
        'All fields (roomId, sender, receiver, message) are required',
      );
    }

    const sender = await this.authModel.findById(senderId);
    const receiver = await this.authModel.findById(receiverId);

    if (!sender || !receiver) {
      throw new Error('Sender or Receiver not found');
    }

    const msg = new this.messageModel({
      roomId: roomId,
      sender: senderId,
      receiver: receiverId,
      requesterId: requesterId,
      message: messageText,
    });

    const savedMessage = await msg.save();

    // Populate sender and receiver info
    const populatedMessage = await this.messageModel
      .findById(savedMessage._id)
      .populate('sender', 'firstName lastName email')
      .populate('receiver', 'firstName lastName email');

    if (this.io) {
      this.io.to(roomId).emit('receiveMessage', populatedMessage);
    }

    return {
      success: true,
      message: 'Message sent',
      savedMessage: populatedMessage,
    };
  }

  async getMessages(roomId: string, userId: string) {
    if (!roomId || !userId) {
      throw new Error('Room ID and User ID are required');
    }

    // Check if user has access to this room
    const room = await this.chatRoomModel.findOne({
      roomId: roomId,
      users: userId,
    });

    if (!room) {
      throw new HttpException(
        'Room not found or access denied',
        HttpStatus.FORBIDDEN,
      );
    }

    const messages = await this.messageModel
      .find({ roomId })
      .populate('sender', 'firstName lastName email')
      .populate('receiver', 'firstName lastName email')
      .sort({ timestamp: 1 });

    return messages;
  }

  async getInbox(userId: string) {
    if (!userId) {
      throw new Error('User ID is required');
    }

    // Get all rooms where user is a participant
    const rooms = await this.chatRoomModel
      .find({ users: userId })
      .sort({ updatedAt: -1 });

    const inbox: any[] = [];

    for (const room of rooms) {
      // Get the last message for each room
      const lastMessage = await this.messageModel
        .findOne({ roomId: room.roomId })
        .populate('sender', 'firstName lastName email')
        .sort({ timestamp: -1 });

      // Get unread message count
      const unreadCount = await this.messageModel.countDocuments({
        roomId: room.roomId,
        receiver: userId,
        isRead: false,
      });

      let chatPartner = null;
      if (!room.isGroup) {
        const partnerId = room.users.find((id) => id !== userId);
        chatPartner = await this.authModel.findById(
          partnerId,
          'firstName lastName email',
        );
      }

      inbox.push({
        roomId: room.roomId,
        isGroup: room.isGroup,
        groupName: room.groupName,
        groupPicture: room.groupPicture,
        chatPartner,
        lastMessage,
        unreadCount,
        updatedAt: (room as any).updatedAt,
      });
    }

    return inbox;
  }

  async getAdminId() {
    const admin = await this.authModel.findOne({ role: 'admin' });
    if (!admin) {
      throw new HttpException('Admin not found', HttpStatus.NOT_FOUND);
    }
    return {
      status: 200,
      message: 'Admin ID retrieved successfully',
      adminId: admin._id,
    };
  }

  async getRoom(userId: string, partnerId: number) {
    const partnerIdStr = partnerId.toString();
    const userIds = [userId, partnerIdStr];
    userIds.sort();

    const room = await this.chatRoomModel.findOne({
      users: { $all: userIds, $size: 2 },
      isGroup: false,
    });

    if (!room) {
      throw new HttpException('Room not found', HttpStatus.NOT_FOUND);
    }

    return {
      roomId: room.roomId,
      users: room.users,
    };
  }

  async markMessageAsRead(messageId: string, userId: string) {
    const message = await this.messageModel.findByIdAndUpdate(
      messageId,
      {
        isRead: true,
        $addToSet: { readUsers: userId },
      },
      { new: true },
    );

    if (!message) {
      throw new HttpException('Message not found', HttpStatus.NOT_FOUND);
    }

    return message;
  }

  async markAllMessagesAsRead(roomId: string, userId: string) {
    const result = await this.messageModel.updateMany(
      {
        roomId: roomId,
        receiver: userId,
        isRead: false,
      },
      {
        isRead: true,
        $addToSet: { readUsers: userId },
      },
    );

    return result;
  }

  async getUnreadMessageCount(userId: string) {
    const count = await this.messageModel.countDocuments({
      receiver: userId,
      isRead: false,
    });

    return count;
  }

  async setupSocketEvents() {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      console.log('🔌 User connected:', socket.id);

      // Join user to their personal room (for notifications)
      socket.on('join', (userId: string) => {
        socket.join(`user_${userId}`);
        console.log(`👤 User ${userId} joined personal room`);
      });

      // Join a chat room
      socket.on('joinRoom', (roomId: string) => {
        socket.join(roomId);
        console.log(`🏠 Socket ${socket.id} joined room: ${roomId}`);
        socket.to(roomId).emit('userJoined', { socketId: socket.id });
      });

      // Leave a chat room
      socket.on('leaveRoom', (roomId: string) => {
        socket.leave(roomId);
        console.log(`🚪 Socket ${socket.id} left room: ${roomId}`);
        socket.to(roomId).emit('userLeft', { socketId: socket.id });
      });

      // Handle real-time message sending
      socket.on(
        'sendMessage',
        async (data: {
          senderId: string;
          roomId: string;
          receiverId: string;
          requesterId: string;
          message?: string;
          mediaUrl?: string;
        }) => {
          try {
            const msg = new this.messageModel({
              roomId: data.roomId,
              sender: data.senderId,
              receiver: data.receiverId,
              requesterId: data.requesterId || data.senderId,
              message: data.message,
              mediaUrl: data.mediaUrl || '',
            });

            const savedMessage = await msg.save();

            // Populate sender and receiver info
            const populatedMessage = await this.messageModel
              .findById(savedMessage._id)
              .populate('sender', 'firstName lastName email')
              .populate('receiver', 'firstName lastName email');

            if (this.io) {
              this.io.to(data.roomId).emit('receiveMessage', populatedMessage);
            }

            // Emit to sender for confirmation with complete message object
            socket.emit('messageSent', {
              success: true,
              data: populatedMessage,
            });

            // Emit new message to room
            this.io.to(data.roomId).emit('newMessage', populatedMessage);

            // Update inbox for receiver
            const receiverInbox = await this.getInbox(data.receiverId);
            this.io
              .to(data.receiverId.toString())
              .emit('updateInbox', receiverInbox);

            // Update inbox for sender too
            const senderInbox = await this.getInbox(data.senderId);
            this.io
              .to(data.senderId.toString())
              .emit('updateInbox', senderInbox);
          } catch (error) {
            socket.emit('messageError', {
              success: false,
              error: error.message,
            });
          }
        },
      );

      socket.on('getInbox', async (userId, callback) => {
        try {
          const inbox = await this.getInbox(userId);
          console.log('get inbox event');
          if (callback) {
            callback({ success: true, data: inbox });
          } else {
            socket.emit('inbox', { success: true, data: inbox });
          }
        } catch (error) {
          if (callback) {
            callback({ success: false, message: error.message });
          } else {
            socket.emit('inbox', { success: false, message: error.message });
          }
        }
      });

      // Handle typing indicators
      socket.on(
        'typing',
        (data: { roomId: string; userId: string; isTyping: boolean }) => {
          socket.to(data.roomId).emit('userTyping', {
            userId: data.userId,
            isTyping: data.isTyping,
          });
        },
      );

      // Handle message read status
      socket.on(
        'markAsRead',
        async (data: { roomId: string; userId: string; messageId: string }) => {
          try {
            await this.messageModel.findByIdAndUpdate(data.messageId, {
              isRead: true,
              $addToSet: { readUsers: data.userId },
            });

            // Notify other users in the room
            socket.to(data.roomId).emit('messageRead', {
              messageId: data.messageId,
              userId: data.userId,
            });
          } catch (error) {
            console.error('Error marking message as read:', error);
          }
        },
      );

      // Handle user online status
      socket.on(
        'setOnlineStatus',
        (data: { userId: string; isOnline: boolean }) => {
          // Broadcast to all rooms this user is part of
          socket.broadcast.emit('userStatusChanged', {
            userId: data.userId,
            isOnline: data.isOnline,
            lastSeen: new Date(),
          });
        },
      );

      socket.on('joinInbox', ({ userId }) => {
        socket.join(userId.toString());
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log('🔌 User disconnected:', socket.id);
        // You can implement logic to update user's last seen status here
      });
    });
  }
}
