import { Injectable, BadRequestException } from '@nestjs/common';
import { uploadToGCS, deleteFromGCS, generateUniqueFileName, UploadResult } from '../../utils/google-cloud-storage';

@Injectable()
export class UploadService {
  
  /**
   * Upload single file to Google Cloud Storage
   * @param file - Multer file object
   * @param folder - Optional folder name (e.g., 'brokers', 'profiles')
   * @returns Upload result with public URL
   */
  async uploadFile(file: Express.Multer.File, folder?: string): Promise<UploadResult> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Validate file type
    this.validateFileType(file);

    // Generate unique file name
    const uniqueFileName = generateUniqueFileName(file.originalname);

    try {
      // Upload to Google Cloud Storage
      const result = await uploadToGCS(file, uniqueFileName, folder);
      
      return result;
    } catch (error) {
      throw new BadRequestException(`File upload failed: ${error.message}`);
    }
  }

  /**
   * Upload multiple files to Google Cloud Storage
   * @param files - Array of Multer file objects
   * @param folder - Optional folder name
   * @returns Array of upload results
   */
  async uploadMultipleFiles(files: Express.Multer.File[], folder?: string): Promise<UploadResult[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    const uploadPromises = files.map(file => this.uploadFile(file, folder));
    
    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      throw new BadRequestException(`Multiple file upload failed: ${error.message}`);
    }
  }

  /**
   * Delete file from Google Cloud Storage
   * @param fileName - Full file name/path in storage
   */
  async deleteFile(fileName: string): Promise<void> {
    try {
      await deleteFromGCS(fileName);
    } catch (error) {
      throw new BadRequestException(`File deletion failed: ${error.message}`);
    }
  }

  /**
   * Validate file type and size
   * @param file - Multer file object
   */
  private validateFileType(file: Express.Multer.File): void {
    // Allowed file types
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      );
    }

    // File size limit (10MB)
    const maxSize = 100 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      throw new BadRequestException('File size too large. Maximum size is 100MB');
    }
  }
}
