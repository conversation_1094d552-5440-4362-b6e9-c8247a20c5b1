import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Put,
  Delete,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { SupportService } from './support.service';
import { CreateSupportDto, AdminReplyDto } from './dto/create-support.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { Public } from '../../Gaurd/public.decorator';

@ApiTags('Support')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('support')
export class SupportController {
  constructor(private readonly supportService: SupportService) {}

  @Post('createSupport')
  @ApiOperation({ summary: 'Create a new support ticket' })
  @ApiBody({ type: CreateSupportDto })
  @ApiResponse({
    status: 201,
    description: 'Support ticket created successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Support created successfully' },
        data: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            name: { type: 'string' },
            category: { type: 'string' },
            description: { type: 'string' },
            email: { type: 'string' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(
    @Body() createSupportDto: CreateSupportDto,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    const result = await this.supportService.createSupport(
      createSupportDto,
      userId,
    );
    return {
      status: 201,
      message: 'Support created successfully',
      data: result,
    };
  }

  @Get('findAllSupports')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all support tickets (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Support tickets fetched successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'number', example: 200 },
        message: {
          type: 'string',
          example: 'Support tickets fetched successfully',
        },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string' },
              userId: { type: 'string' },
              name: { type: 'string' },
              category: { type: 'string' },
              description: { type: 'string' },
              email: { type: 'string' },
              isReplied: { type: 'boolean' },
              adminReplyTitle: { type: 'string' },
              adminReplyMessage: { type: 'string' },
              adminId: { type: 'string' },
              repliedAt: { type: 'string' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async findAll() {
    const result = await this.supportService.findAll();
    return {
      status: 200,
      message: 'Support tickets fetched successfully',
      data: result,
    };
  }

  @Public()
  @Get('userAllSupports/:userId')
  @ApiOperation({
    summary: 'Get all support tickets for a specific user',
  })
  @ApiResponse({
    status: 200,
    description: 'Support tickets retrieved successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
  })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async findSupportsByUserId(@Param('userId') userId: string) {
    try {
      // No token check anymore
      // Admin check also not needed unless you want to restrict this publicly

      const supports = await this.supportService.findSupportsByUserId(userId);

      return {
        status: 200,
        message: 'User support tickets fetched successfully',
        data: supports,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('replyToSupport/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Admin reply to support ticket' })
  @ApiBody({ type: AdminReplyDto })
  async replyToSupport(
    @Param('id') id: string,
    @Body() adminReplyDto: AdminReplyDto,
    @Request() req: any,
  ) {
    const adminId = req.user.userId;
    const result = await this.supportService.replyToSupport(
      id,
      adminReplyDto,
      adminId,
    );
    return {
      status: 200,
      message: 'Reply sent successfully',
      data: result,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific support ticket by ID' })
  @ApiResponse({
    status: 200,
    description: 'Support ticket fetched successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Support ticket not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async findOne(@Param('id') id: string) {
    const result = await this.supportService.findOne(id);
    return {
      status: 200,
      message: 'Support ticket fetched successfully',
      data: result,
    };
  }

  @Put('closeSupport/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Close a support ticket by ID' })
  @ApiResponse({
    status: 200,
    description: 'Support ticket closed successfully',
  })
  @ApiResponse({ status: 404, description: 'Support ticket not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async closeSupport(@Param('id') id: string, @Request() req) {
    const adminId = req.user.userId;
    const result = await this.supportService.closeSupport(id, adminId);
    return {
      status: 200,
      message: 'Support ticket closed successfully',
      data: result,
    };
  }
}
