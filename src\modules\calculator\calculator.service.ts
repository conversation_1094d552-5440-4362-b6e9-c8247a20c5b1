import { Injectable } from '@nestjs/common';
import { TradingCalculatorDto } from './dto/calculator.dto';

export interface CalculatorResult {
  monthlyProfit: number;
  yearlyProfit: number;
  dailyProfit: number;
  input: TradingCalculatorDto;
}

@Injectable()
export class CalculatorService {

  calculateTradingProfit(calculatorDto: TradingCalculatorDto): CalculatorResult {
    const { unit, lots, tradesPerDay, rebate } = calculatorDto;

    // Base calculation per trade
    let profitPerTrade: number;

    if (unit === 'pips') {
      // For pips: rebate * lots * pip value (1 pip = $10 for standard lot)
      const pipValue = lots * 10; // $10 per pip for 1 lot, $1 for 0.1 lot, $0.1 for 0.01 lot
      profitPerTrade = rebate * pipValue;
    } else {
      // For USD: rebate amount per trade * lots
      profitPerTrade = rebate * lots;
    }

    // Calculate daily, monthly, and yearly profits
    const dailyProfit = profitPerTrade * tradesPerDay;
    const yearlyProfit = dailyProfit * 252; // 252 trading days per year
    const monthlyProfit = yearlyProfit / 12; // Monthly = Yearly / 12 months

    return {
      dailyProfit: Math.round(dailyProfit * 100) / 100, // Round to 2 decimal places
      monthlyProfit: Math.round(monthlyProfit * 100) / 100,
      yearlyProfit: Math.round(yearlyProfit * 100) / 100,
      input: calculatorDto
    };
  }
}
