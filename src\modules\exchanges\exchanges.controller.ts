import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ExchangesService } from './exchanges.service';
import {
  CreateExchangeDto,
  UpdateExchangeDto,
  RateExchangeDto,
} from './dto/exchange.dto';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';
import { Public } from '../../Gaurd/public.decorator';

@ApiTags('Exchanges')
@Controller('exchanges')
@UseGuards(JwtAuthGuard)
export class ExchangesController {
  constructor(private readonly exchangesService: ExchangesService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create a new exchange (Admin only)' })
  @ApiResponse({ status: 201, description: 'Exchange created successfully' })
  @ApiResponse({
    status: 409,
    description: 'Exchange with this title already exists',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createExchangeDto: CreateExchangeDto, @Request() req: any) {
    const userId = req.user.userId;
    return this.exchangesService.create(createExchangeDto, userId);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all active exchanges' })
  @ApiResponse({ status: 200, description: 'List of all active exchanges' })
  findAll() {
    return this.exchangesService.findAll();
  }

  @Get('admin/all')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all exchanges (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of all exchanges' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Admin access required' })
  getAllAdmin(@Request() req: any) {
    const userId = req.user.userId;
    return this.exchangesService.findAllAdmin(userId);
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get exchange by ID' })
  @ApiResponse({ status: 200, description: 'Exchange found' })
  @ApiResponse({ status: 404, description: 'Exchange not found' })
  findOne(@Param('id') id: string) {
    return this.exchangesService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Update exchange (Admin only)' })
  @ApiResponse({ status: 200, description: 'Exchange updated successfully' })
  @ApiResponse({ status: 404, description: 'Exchange not found' })
  @ApiResponse({
    status: 409,
    description: 'Exchange with this title already exists',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  update(
    @Param('id') id: string,
    @Body() updateExchangeDto: UpdateExchangeDto,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    return this.exchangesService.update(id, updateExchangeDto, userId);
  }

  @Post(':id/rate')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Rate an exchange' })
  @ApiResponse({ status: 200, description: 'Exchange rated successfully' })
  @ApiResponse({ status: 404, description: 'Exchange not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  rateExchange(
    @Param('id') id: string,
    @Body() rateExchangeDto: RateExchangeDto,
    @Request() req: any,
  ) {
    const userId = req.user.userId;
    return this.exchangesService.rateExchange(id, rateExchangeDto, userId);
  }

  @Delete(':id')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Delete exchange (Admin only)' })
  @ApiResponse({ status: 200, description: 'Exchange deleted successfully' })
  @ApiResponse({ status: 404, description: 'Exchange not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  remove(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.exchangesService.remove(id, userId);
  }
}
