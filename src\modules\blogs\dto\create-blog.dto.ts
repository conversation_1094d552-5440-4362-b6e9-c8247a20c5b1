import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBlogDto {
  @ApiProperty({ description: 'Blog title' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Main Image' })
  @IsString()
  @IsNotEmpty()
  mainImage: string;

  @ApiProperty({ description: 'CTA Button Text' })
  @IsString()
  @IsNotEmpty()
  CTAButtonText: string;

  @ApiProperty({ description: 'Blog content' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: 'Blog image URL' })
  @IsString()
  @IsNotEmpty()
  image: string;

  @ApiProperty({ description: 'Blog thumbnail image URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailImage?: string;

  @ApiProperty({ description: 'Call to action title', required: false })
  @IsString()
  @IsOptional()
  ctaTitle?: string;

  @ApiProperty({ description: 'Blog subtext', required: false })
  @IsString()
  @IsOptional()
  subtext?: string;

  @ApiProperty({ description: 'Call to action image URL', required: false })
  @IsString()
  @IsOptional()
  ctaImage?: string;

  @ApiProperty({ description: 'Blog link URL', required: false })
  @IsString()
  @IsOptional()
  link?: string;
}
