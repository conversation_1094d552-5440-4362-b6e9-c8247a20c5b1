import {
  Injectable,
  NotFoundException,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Broker } from './entity/broker';
import {
  CreateBrokerDto,
  UpdateBrokerDto,
  RateBrokerDto,
  SendEmailDto,
} from './dto/broker.dto';
import { Auth } from '../auth/entities/auth.entity';
import { ManageAccount } from '../manage-accounts/entities/manage-account.entity';
import { MailService } from '../../utils/sendMail';
import { DashboardService } from '../dashboard/dashboard.service';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class BrokerService {
  constructor(
    @InjectModel(Broker.name) private brokerModel: Model<Broker>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
    @InjectModel(ManageAccount.name)
    private manageAccountModel: Model<ManageAccount>,
    private mailService: MailService,
    private dashboardService: DashboardService,
    private notificationsService: NotificationsService,
  ) {}

  async create(
    createBrokerDto: CreateBrokerDto,
    userId: string,
  ): Promise<Broker> {
    try {
      const user = await this.authModel.findById(userId);
      if (user?.role !== 'admin') {
        throw new UnauthorizedException(
          'You are not authorized to create a broker',
        );
      }

      // Check if broker with same name already exists
      const existingBroker = await this.brokerModel.findOne({
        brokerName: createBrokerDto.brokerName,
      });

      if (existingBroker) {
        throw new ConflictException('Broker with this name already exists');
      }

      const createdBroker = new this.brokerModel(createBrokerDto);
      const savedBroker = await createdBroker.save();

      await this.dashboardService.incrementBrokerCount();

      return savedBroker;
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to create broker: ${error.message}`);
    }
  }

  async findAll(userId?: string): Promise<Broker[]> {
    try {
      // Admin check if userId is provided
      if (userId) {
        const user = await this.authModel.findById(userId);
        if (!user || user.role !== 'admin') {
          throw new UnauthorizedException('Admin access required');
        }
      }

      return await this.brokerModel
        .aggregate([
          {
            $addFields: {
              connectedUsersCount: {
                $size: { $ifNull: ['$connectedUsers', []] },
              },
            },
          },
          {
            $lookup: {
              from: 'csvhistories',
              localField: '_id',
              foreignField: 'brokerId',
              as: 'csvHistory'
            }
          },
          {
            $addFields: {
              csvHistory: {
                $slice: [
                  {
                    $sortArray: {
                      input: '$csvHistory',
                      sortBy: { createdAt: -1 }
                    }
                  },
                  1
                ]
              }
            }
          },
          {
            $addFields: {
              csvHistory: {
                $map: {
                  input: '$csvHistory',
                  as: 'history',
                  in: {
                    startDate: '$$history.startDate',
                    endDate: '$$history.endDate',
                    totalRebate: '$$history.totalRebate'
                  }
                }
              }
            }
          },
          {
            $sort: { connectedUsersCount: -1 },
          },
        ])
        .exec();
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new Error(`Failed to fetch brokers: ${error.message}`);
    }
  }

  async findAllPublic(): Promise<Broker[]> {
    try {
      return await this.brokerModel
        .aggregate([
          {
            $addFields: {
              connectedUsersCount: {
                $size: { $ifNull: ['$connectedUsers', []] },
              },
            },
          },
          {
            $lookup: {
              from: 'csvhistories',
              localField: '_id',
              foreignField: 'brokerId',
              as: 'csvHistory'
            }
          },
          {
            $addFields: {
              csvHistory: {
                $slice: [
                  {
                    $sortArray: {
                      input: '$csvHistory',
                      sortBy: { createdAt: -1 }
                    }
                  },
                  1
                ]
              }
            }
          },
          {
            $addFields: {
              csvHistory: {
                $map: {
                  input: '$csvHistory',
                  as: 'history',
                  in: {
                    startDate: '$$history.startDate',
                    endDate: '$$history.endDate',
                    totalRebate: '$$history.totalRebate'
                  }
                }
              }
            }
          },
          {
            $sort: { connectedUsersCount: -1 },
          },
        ])
        .exec();
    } catch (error) {
      throw new Error(`Failed to fetch brokers: ${error.message}`);
    }
  }

  async getTopLeaderBoardBrokers(): Promise<Broker[]> {
    try {
      return await this.brokerModel
        .aggregate([
          {
            $addFields: {
              connectedUsersCount: {
                $size: { $ifNull: ['$connectedUsers', []] },
              },
            },
          },
          {
            $sort: { connectedUsersCount: -1 },
          },
          {
            $limit: 4,
          },
        ])
        .exec();
    } catch (error) {
      throw new Error(`Failed to fetch top 4 brokers: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<Broker> {
    try {
      const broker = await this.brokerModel.findById(id).exec();
      if (!broker) {
        throw new NotFoundException(`Broker with ID ${id} not found`);
      }
      return broker;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch broker: ${error.message}`);
    }
  }

  async update(
    id: string,
    updateBrokerDto: UpdateBrokerDto,
    userId: string,
  ): Promise<Broker> {
    try {
      const user = await this.authModel.findById(userId);
      if (user?.role !== 'admin') {
        throw new UnauthorizedException(
          'You are not authorized to update a broker',
        );
      }

      const existingBroker = await this.brokerModel.findById(id);
      if (!existingBroker) {
        throw new NotFoundException(`Broker with ID ${id} not found`);
      }

      // If updating broker name, check for conflicts
      if (updateBrokerDto.brokerName) {
        const brokerWithSameName = await this.brokerModel.findOne({
          brokerName: updateBrokerDto.brokerName,
          _id: { $ne: id },
        });

        if (brokerWithSameName) {
          throw new ConflictException('Broker with this name already exists');
        }
      }

      // Remove _id and any other fields that shouldn't be updated
      const { _id, ...updateData } = updateBrokerDto as any;

      // Create update object with only provided fields
      const updateFields: any = {};

      // Only add fields that are actually provided in the request
      Object.keys(updateData).forEach((key) => {
        if (updateData[key] !== undefined && updateData[key] !== null) {
          updateFields[key] = updateData[key];
        }
      });

      // Ensure brokerId cannot be changed (if it exists in the DTO)
      if ('brokerId' in updateFields) {
        delete updateFields.brokerId;
      }

      // Auto-activate broker if both required fields are filled
      if (existingBroker.status === 'draft') {
        // Get the updated broker data (merge existing with updates)
        const updatedData = { ...existingBroker.toObject(), ...updateFields };

        // Check if both required fields are filled
        const isDataComplete =
          updatedData.brokerName &&
          updatedData.brokerName.trim() !== '' &&
          updatedData.brokerImage &&
          updatedData.brokerImage.trim() !== '' &&
          updatedData.profitPercentage &&
          updatedData.profitPercentage > 0;
          

        if (isDataComplete) {
          updateFields.status = 'active';
        }
      }

      // Use $set to only update provided fields, keeping others unchanged
      const updatedBroker = await this.brokerModel
        .findByIdAndUpdate(
          id,
          { $set: updateFields },
          { new: true, runValidators: true },
        )
        .exec();

      if (!updatedBroker) {
        throw new NotFoundException(`Broker with ID ${id} not found`);
      }

      return updatedBroker;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to update broker: ${error.message}`);
    }
  }

  async remove(id: string): Promise<{ message: string }> {
    try {
      const deletedBroker = await this.brokerModel.findByIdAndDelete(id).exec();
      if (!deletedBroker) {
        throw new NotFoundException(`Broker with ID ${id} not found`);
      }

      await this.dashboardService.decrementBrokerCount();

      return { message: 'Broker deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to delete broker: ${error.message}`);
    }
  }

  async rateBroker(
    brokerId: string,
    userId: string,
    rateBrokerDto: RateBrokerDto,
  ): Promise<Broker> {
    try {
      const broker = await this.brokerModel.findById(brokerId);
      if (!broker) {
        throw new NotFoundException(`Broker with ID ${brokerId} not found`);
      }

      // Check if user is connected to this broker
      const isConnected = broker.connectedUsers.some(
        (connectedUser) => connectedUser.userId.toString() === userId.toString()
      );
      
      if (!isConnected) {
        throw new BadRequestException('You must be connected to this broker to rate it');
      }

      // Check if user has already rated this broker
      const existingRatingIndex = broker.userRatings.findIndex(
        (rating) => rating.userId.toString() === userId.toString(),
      );

      if (existingRatingIndex !== -1) {
        // Update existing rating
        broker.userRatings[existingRatingIndex].rating = rateBrokerDto.rating;
        broker.userRatings[existingRatingIndex].review = rateBrokerDto.review || '';
        broker.userRatings[existingRatingIndex].ratedAt = new Date();
      } else {
        // Add new rating
        const user = await this.authModel.findById(userId);

        broker.userRatings.push({
          userId: userId as any,
          rating: rateBrokerDto.rating,
          userName: user?.firstName + ' ' + user?.lastName,
          ratedAt: new Date(),
          review: rateBrokerDto.review || '',
        });
        broker.totalRatings += 1;
      }

      // Calculate average rating
      const totalRatingSum = broker.userRatings.reduce(
        (sum, rating) => sum + rating.rating,
        0,
      );
      broker.ratings =
        Math.round((totalRatingSum / broker.userRatings.length) * 10) / 10;

      const savedBroker = await broker.save();

      // Create review notification for the user
      try {
        await this.notificationsService.create({
          userId: userId,
          title: 'Review Submitted',
          message: `Your review for ${broker.brokerName} has been submitted successfully.`,
          type: 'review',
          metadata: { 
            brokerId: brokerId,
            rating: rateBrokerDto.rating,
            brokerName: broker.brokerName
          }
        });
      } catch (error) {
        console.error(`❌ Failed to send review notification:`, error);
      }

      return savedBroker;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new Error(`Failed to rate broker: ${error.message}`);
    }
  }

  async getConnectedBrokers(userId: string): Promise<any[]> {
    try {
      console.log('userId in getConnectedBrokers', userId);

      // Convert string userId to ObjectId for proper comparison
      const userObjectId = new Types.ObjectId(userId);

      // Find all brokers where user ID exists in connectedUsers array
      const connectedBrokers = await this.brokerModel
        .find({
          connectedUsers: { $in: [userObjectId] },
        })
        .sort({ connectedUsersCount: -1 })
        .exec();
      console.log('connected Brokers', connectedBrokers);

      
      const brokersWithAccountNumbers = await Promise.all(
        connectedBrokers.map(async (broker) => {
         
          const userAccount = await this.manageAccountModel
            .findOne({
              userId: userObjectId,
              brokerId: broker._id,
            })
            .exec();

          return {
            ...broker.toObject(),
            accountNo: userAccount ? userAccount.accountNo : null,
            accountStatus: userAccount ? userAccount.status : null,
            joinedAt: userAccount ? userAccount.joinedAt : null,
            totalProfit: userAccount ? userAccount.totalProfit : 0,
          };
        }),
      );

      return brokersWithAccountNumbers;
    } catch (error) {
      throw new Error(`Failed to fetch connected brokers: ${error.message}`);
    }
  }

  async sendEmail(
    sendEmailDto: SendEmailDto,
    userId: string,
  ): Promise<{ message: string }> {
    try {
      const user = await this.authModel.findById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Get broker's defaultCC if available
      const broker = await this.brokerModel.findOne({ userId: userId });
      const brokerDefaultCC = broker?.email?.defaultCC;

      await this.mailService.sendCustomEmail(
        sendEmailDto.email,
        user.email,
        sendEmailDto.subject,
        sendEmailDto.body,
        sendEmailDto.cc,
        brokerDefaultCC,
      );

      return { message: 'Email sent successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async deleteUserRating(
    brokerId: string,
    userId: string,
    adminId: string
  ): Promise<Broker> {
    try {
      const admin = await this.authModel.findById(adminId);
      
      if (!admin || admin?.role !== 'admin') {
        throw new UnauthorizedException('Only admin can delete user rating');
      }

      if (!userId) {
        throw new NotFoundException(`User not found`);
      }

      const user = await this.authModel.findById(userId);
      if (!user) {
        throw new NotFoundException(`User not found`);
      }

      const broker = await this.brokerModel.findById(brokerId);
      if (!broker) {
        throw new NotFoundException(`Broker with ID ${brokerId} not found`);
      }

      // Find the user's rating index
      const existingRatingIndex = broker.userRatings.findIndex(
        (rating) => rating.userId.toString() === userId.toString(),
      );

      if (existingRatingIndex === -1) {
        throw new NotFoundException('User rating not found for this broker');
      }

      // Remove the user's rating
      broker.userRatings.splice(existingRatingIndex, 1);
      broker.totalRatings = Math.max(0, broker.totalRatings - 1);

      // Recalculate average rating
      if (broker.userRatings.length > 0) {
        const totalRatingSum = broker.userRatings.reduce(
          (sum, rating) => sum + rating.rating,
          0,
        );
        broker.ratings =
          Math.round((totalRatingSum / broker.userRatings.length) * 10) / 10;
      } else {
        broker.ratings = 0;
      }

      return await broker.save();
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new Error(`Failed to delete rating: ${error.message}`);
    }
  }

  async toggleFeaturedStatus(id: string, userId: string): Promise<Broker> {
    try {
      // Check if user is admin
      const user = await this.authModel.findById(userId);
      if (!user || user.role !== 'admin') {
        throw new UnauthorizedException('Admin access required');
      }

      // Get current broker to check current featured status
      const currentBroker = await this.brokerModel.findById(id);
      if (!currentBroker) {
        throw new NotFoundException(`Broker with ID ${id} not found`);
      }

      // Toggle the featured status
      const newFeaturedStatus = !currentBroker.isFeatured;

      const updatedBroker = await this.brokerModel
        .findByIdAndUpdate(
          id,
          { $set: { isFeatured: newFeaturedStatus } },
          { new: true, runValidators: true },
        )
        .exec();

        if(!updatedBroker){
          throw new NotFoundException(`Broker with ID ${id} not found`);
        }

      return updatedBroker;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }
      throw new Error(`Failed to update broker featured status: ${error.message}`);
    }
  }
}
