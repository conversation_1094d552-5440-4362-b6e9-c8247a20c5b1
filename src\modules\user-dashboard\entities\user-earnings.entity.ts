import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class UserEarnings extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  date: Date; // Daily date for graph

  @Prop({ default: 0 })
  totalTradeProfit: number;

  @Prop({ default: 0 })
  level1ReferralEarnings: number;

  @Prop({ default: 0 })
  level2ReferralEarnings: number;

  @Prop({ default: 0 })
  level3ReferralEarnings: number;

  @Prop({ default: 0 })
  totalDailyEarnings: number; // Sum of all earnings for the day
}

export const UserEarningsSchema = SchemaFactory.createForClass(UserEarnings);

// Create index for efficient queries
UserEarningsSchema.index({ userId: 1, date: 1 }, { unique: true });