import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { LiveChatService } from './chat.service';
import { AuthGuard } from '@nestjs/passport';

@Controller('chat')
export class LiveChatController {
  constructor(private readonly liveChatService: LiveChatService) {}

  @Post('create-room')
  @UseGuards(AuthGuard('jwt'))
  async createRoom(@Req() req, @Body() body) {
    const sender = req.user.userId;
    const { receiver, requesterId } = body;
    return this.liveChatService.createRoom(sender, receiver, requesterId);
  }

  @Post('send-message')
  @UseGuards(AuthGuard('jwt'))
  async sendMessage(@Req() req, @Body() body) {
    const sender = req.user.userId;
    const { roomId, receiver, message, requester } = body;
    console.log('sender', sender);
    console.log('roomId', roomId);
    console.log('receiver', receiver);
    console.log('message', message);

    return this.liveChatService.sendMessage(
      sender,
      roomId,
      receiver,
      requester,
      message,
    );
  }

  @Get('adminId')
  @UseGuards(AuthGuard('jwt'))
  async getAdminId() {
    return this.liveChatService.getAdminId();
  }

  @Get('messages/:roomId')
  @UseGuards(AuthGuard('jwt'))
  async getMessages(@Param('roomId') roomId: string, @Req() req) {
    const userId = req.user.userId;
    return this.liveChatService.getMessages(roomId, userId);
  }

  @Get('inbox')
  @UseGuards(AuthGuard('jwt'))
  async getInbox(@Req() req) {
    const userId = req.user.userId;
    return this.liveChatService.getInbox(userId);
  }

  @Post('room/:id')
  @UseGuards(AuthGuard('jwt'))
  async getRoom(@Req() req, @Param('id') partnerId: string) {
    const userId = req.user.userId;
    return this.liveChatService.getRoom(userId, Number(partnerId));
  }
}
