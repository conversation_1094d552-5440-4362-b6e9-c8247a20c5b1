import { Connection, Keypair, PublicKey, Transaction, SystemProgram, sendAndConfirmTransaction } from '@solana/web3.js';
import { createTransferInstruction, getAssociatedTokenAddress, createAssociatedTokenAccountInstruction, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import bs58 from 'bs58';

// Function to transfer SOL or SPL tokens to multiple recipients
export async function executeSolanaTransfer(
  recipients: string[], 
  amounts: string[], 
  tokenMint?: string
): Promise<{ success: boolean; message: string; txHash?: string }> {
  try {
    // Validate inputs
    if (!recipients || !amounts || recipients.length === 0 || amounts.length === 0) {
      throw new Error('Recipients and amounts arrays cannot be empty');
    }

    if (recipients.length !== amounts.length) {
      throw new Error('Recipients and amounts arrays must have the same length');
    }

    


    const privateKeyBytes = process.env.SOLANA_PRIVATE_KEY? JSON.parse(process.env.SOLANA_PRIVATE_KEY) : [];
    const privateKey = convertToBase58PrivateKey(privateKeyBytes);
    console.log('Your Solana private key in base58 format:');
    console.log(base58PrivateKey);
    const rpcUrl = process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com';

    if (!privateKey) {
      throw new Error('SOLANA_PRIVATE_KEY must be set in environment variables');
    }

    // Setup connection to Solana network
    console.log(`🔄 Connecting to Solana network: ${rpcUrl}`);
    const connection = new Connection(rpcUrl, 'confirmed');

    // Create wallet from private key
    const secretKey = Uint8Array.from(bs58.decode(privateKey));
    const wallet = Keypair.fromSecretKey(secretKey);
    
    console.log('🚀 Starting Solana transfer process...');
    console.log('📊 Recipients:', recipients.length);
    
    // Check if we're transferring SOL or SPL tokens
    if (tokenMint) {
      // SPL Token transfer
      return await transferSplTokens(connection, wallet, recipients, amounts, tokenMint);
    } else {
      // Native SOL transfer
      return await transferSol(connection, wallet, recipients, amounts);
    }
  } catch (error) {
    console.error('❌ Solana transfer failed:', error);
    return {
      success: false,
      message: `Solana transfer failed: ${error.message}`
    };
  }
}

// Helper function to transfer native SOL
async function transferSol(
  connection: Connection, 
  wallet: Keypair, 
  recipients: string[], 
  amounts: string[]
): Promise<{ success: boolean; message: string; txHash?: string }> {
  try {
    // Calculate total amount needed (in lamports)
    const totalLamports = amounts.reduce(
      (sum, amount) => sum + Math.round(parseFloat(amount) * 1e9), // Convert SOL to lamports
      0
    );
    
    console.log('💰 Total amount:', totalLamports / 1e9, 'SOL');

    // Check wallet balance
    const balance = await connection.getBalance(wallet.publicKey);
    console.log('💳 Wallet balance:', balance / 1e9, 'SOL');

    if (balance < totalLamports) {
      throw new Error(`Insufficient balance. Required: ${totalLamports / 1e9} SOL, Available: ${balance / 1e9} SOL`);
    }

    // Process transfers in batches to avoid transaction size limits
    const batchSize = 5;
    const batches = Math.ceil(recipients.length / batchSize);
    const successfulTxs: string[] = [];

    for (let i = 0; i < batches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, recipients.length);
      const batchRecipients = recipients.slice(start, end);
      const batchAmounts = amounts.slice(start, end);

      console.log(`🔄 Processing batch ${i + 1}/${batches} with ${batchRecipients.length} recipients`);

      const transaction = new Transaction();

      // Add transfer instructions to transaction
      for (let j = 0; j < batchRecipients.length; j++) {
        const recipientPubkey = new PublicKey(batchRecipients[j]);
        const lamports = Math.round(parseFloat(batchAmounts[j]) * 1e9); // Convert SOL to lamports

        transaction.add(
          SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: recipientPubkey,
            lamports,
          })
        );
      }

      // Send and confirm transaction
      const txHash = await sendAndConfirmTransaction(connection, transaction, [wallet]);
      successfulTxs.push(txHash);
      console.log(`✅ Batch ${i + 1} completed. Transaction hash:`, txHash);
    }

    return {
      success: true,
      message: `Solana transfer completed successfully! Distributed SOL to ${recipients.length} recipients`,
      txHash: successfulTxs.join(',')
    };
  } catch (error) {
    throw error;
  }
}

// Helper function to transfer SPL tokens
async function transferSplTokens(
  connection: Connection, 
  wallet: Keypair, 
  recipients: string[], 
  amounts: string[],
  tokenMintAddress: string
): Promise<{ success: boolean; message: string; txHash?: string }> {
  try {
    const tokenMint = new PublicKey(tokenMintAddress);
    
    // Get source token account
    const sourceTokenAccount = await getAssociatedTokenAddress(
      tokenMint,
      wallet.publicKey
    );
    
    // Check if source token account exists
    try {
      await getAccount(connection, sourceTokenAccount);
    } catch (error) {
      throw new Error(`Source token account does not exist or is not initialized`);
    }
    
    // Get token decimals
    const tokenMintInfo = await connection.getParsedAccountInfo(tokenMint);
    const decimals = tokenMintInfo.value?.data['parsed']['info']['decimals'] || 9;
    
    // Calculate total amount needed
    const totalAmount = amounts.reduce(
      (sum, amount) => sum + Math.round(parseFloat(amount) * 10 ** decimals),
      0
    );
    
    console.log('💰 Total amount:', totalAmount / (10 ** decimals), 'tokens');

    // Check token balance
    const tokenAccountInfo = await connection.getParsedAccountInfo(sourceTokenAccount);
    const tokenBalance = tokenAccountInfo.value?.data['parsed']['info']['tokenAmount']['amount'] || 0;
    console.log('💳 Token balance:', tokenBalance / (10 ** decimals));

    if (parseInt(tokenBalance) < totalAmount) {
      throw new Error(`Insufficient token balance. Required: ${totalAmount / (10 ** decimals)}, Available: ${parseInt(tokenBalance) / (10 ** decimals)}`);
    }

    // Process transfers in batches
    const batchSize = 5;
    const batches = Math.ceil(recipients.length / batchSize);
    const successfulTxs: string[] = [];

    for (let i = 0; i < batches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, recipients.length);
      const batchRecipients = recipients.slice(start, end);
      const batchAmounts = amounts.slice(start, end);

      console.log(`🔄 Processing batch ${i + 1}/${batches} with ${batchRecipients.length} recipients`);

      // Create a new transaction for this batch
      const transaction = new Transaction();

      // Process each recipient in the batch
      for (let j = 0; j < batchRecipients.length; j++) {
        const recipientPubkey = new PublicKey(batchRecipients[j]);
        const amount = Math.round(parseFloat(batchAmounts[j]) * 10 ** decimals);

        // Get recipient's associated token account
        const recipientTokenAccount = await getAssociatedTokenAddress(
          tokenMint,
          recipientPubkey
        );

        // Check if recipient token account exists, if not create it
        try {
          await getAccount(connection, recipientTokenAccount);
        } catch (error) {
          // Account doesn't exist, add instruction to create it
          transaction.add(
            createAssociatedTokenAccountInstruction(
              wallet.publicKey,
              recipientTokenAccount,
              recipientPubkey,
              tokenMint
            )
          );
        }

        // Add transfer instruction
        transaction.add(
          createTransferInstruction(
            sourceTokenAccount,
            recipientTokenAccount,
            wallet.publicKey,
            amount
          )
        );
      }

      // Send and confirm transaction
      const txHash = await sendAndConfirmTransaction(connection, transaction, [wallet]);
      successfulTxs.push(txHash);
      console.log(`✅ Batch ${i + 1} completed. Transaction hash:`, txHash);
    }

    return {
      success: true,
      message: `SPL token transfer completed successfully! Distributed tokens to ${recipients.length} recipients`,
      txHash: successfulTxs.join(',')
    };
  } catch (error) {
    throw error;
  }
}

// Example usage function for testing
export async function testSolanaTransfer(): Promise<void> {
  // Example data - replace with actual user wallet addresses and amounts
  const recipients = [
    '5YNmS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CmPEwKgVWr8', // Example wallet 1
    'ES8bLmG4r7PNkZawxiXG8PuPSyXJGLLpXJ8mZ2NF2YzQ', // Example wallet 2
  ];

  const amounts = [
    '0.01',  // 0.01 SOL to user 1
    '0.02',  // 0.02 SOL to user 2
  ];

  console.log('🧪 Testing Solana transfer function...');
  console.log('📋 Recipients:', recipients);
  console.log('💰 Amounts:', amounts, 'SOL');

  const result = await executeSolanaTransfer(recipients, amounts);

  if (result.success) {
    console.log('✅ Test successful:', result.message);
    console.log('🔗 Transaction hash:', result.txHash);
  } else {
    console.log('❌ Test failed:', result.message);
  }
}


// Convert raw byte array to base58 private key
function convertToBase58PrivateKey(byteArray: number[]): string {
  const uint8Array = new Uint8Array(byteArray);
  return bs58.encode(uint8Array);
}

// Your raw private key bytes
const privateKeyBytes = [43,237,158,174,101,95,34,122,177,121,173,53,41,157,221,179,86,248,251,14,44,151,199,164,156,35,66,8,228,99,96,227,137,11,109,14,12,204,153,222,101,27,163,49,102,210,131,193,55,194,129,74,48,92,125,137,127,120,32,123,201,134,135,146];

// Convert to base58
const base58PrivateKey = convertToBase58PrivateKey(privateKeyBytes);
console.log('Your Solana private key in base58 format:');
console.log(base58PrivateKey);
