import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PlatformFee } from './entities/platfomFee';
import { AddPlatformFeeDto } from './dto/platform-fee.dto';
import { Auth } from '../auth/entities/auth.entity';
import { use } from 'passport';

@Injectable()
export class PlatformFeeService {
  constructor(
    @InjectModel(PlatformFee.name) private platformFeeModel: Model<PlatformFee>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
  ) {}

  async addFee(addPlatformFeeDto: AddPlatformFeeDto, adminId: string) {
    console.log('💰 Adding/Updating platform fees by admin:', adminId);

    // Check if user is admin
    const admin = await this.authModel.findById(adminId);
    if (!admin || admin.role !== 'admin') {
      throw new BadRequestException('Only admin can manage platform fees');
    }

    // Check if platform fee document already exists
    let platformFee = await this.platformFeeModel.findOne();

    if (platformFee) {
      // Update existing document with only provided fields
      const updateData: any = {};
      
      if (addPlatformFeeDto.ethFee !== undefined) {
        updateData.ethFee = addPlatformFeeDto.ethFee;
      }
      if (addPlatformFeeDto.solFee !== undefined) {
        updateData.solFee = addPlatformFeeDto.solFee;
      }
      if (addPlatformFeeDto.btcFee !== undefined) {
        updateData.btcFee = addPlatformFeeDto.btcFee;
      }

      platformFee = await this.platformFeeModel.findOneAndUpdate(
        {},
        updateData,
        { new: true }
      );

      console.log('✅ Platform fees updated successfully');
      return {
        message: 'Platform fees updated successfully',
        data: platformFee
      };
    } else {
      // Create new document
      const newPlatformFee = new this.platformFeeModel({
        ethFee: addPlatformFeeDto.ethFee || 0,
        solFee: addPlatformFeeDto.solFee || 0,
        btcFee: addPlatformFeeDto.btcFee || 0
      });

      const savedFee = await newPlatformFee.save();
      console.log('✅ Platform fees created successfully');

      return {
        message: 'Platform fees created successfully',
        data: savedFee
      };
    }
  }

  async getFee(userId?: string) {
    console.log('📋 Getting platform fees');

    const platformFee = await this.platformFeeModel.findOne();
  
      let user = await this.authModel.findById(userId).select('-password');
    

    if (!platformFee) {
      // Return default fees if no document exists
      return {
        message: 'Platform fees retrieved successfully',
        data: {
          ethFee: 0,
          solFee: 0,
          btcFee: 0,
          withdrawl: user ? user.totalWithdrawn : 0,
          rebates:user?user.totalRebate:0,
          affilliateEarnings:user?user.totalReferralEarnings:0,
          balance:user?user.balance:0

        },
       
      };
    }

    

    return {
      message: 'Platform fees retrieved successfully',
       data: {
          ethFee: platformFee.ethFee,
          solFee: platformFee.solFee,
          btcFee: platformFee.btcFee,
          withdrawl: user ? user.totalWithdrawn : 0,
          rebates:user?user.totalRebate:0,
          affilliateEarnings:user?user.totalReferralEarnings:0,
          balance:user?user.balance:0

        },
      
    };
  }
}

