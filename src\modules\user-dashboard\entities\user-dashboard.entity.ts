import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class UserDashboard extends Document {
  @Prop({ required: true, ref: 'Auth' })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  month: number; // 1-12 for January-December

  @Prop({ required: true })
  year: number;

  @Prop({ default: 0 })
  availableBalance: number;

  @Prop({ default: 0 })
  totalRebates: number;

  @Prop({ default: 0 })
  level1ReferralEarnings: number;

  @Prop({ default: 0 })
  level2ReferralEarnings: number;

  @Prop({ default: 0 })
  level3ReferralEarnings: number;

  @Prop({ default: 0 })
  totalTradeProfit: number;

  
}

export const UserDashboardSchema = SchemaFactory.createForClass(UserDashboard);