import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Profit } from './entities/profit.entity';
import { ReferralProfits } from './entities/referral-profits.entity';
import { CreditDebitHistory } from './entities/credit-debit-history';
import { ManageAccount } from '../manage-accounts/entities/manage-account.entity';
import { ExchangeAccount } from '../manage-accounts/entities/exchange-account.entity';
import { Auth } from '../auth/entities/auth.entity';
import { ReferralTree } from '../auth/entities/referral-tree.entity';
import { CsvRowData } from './dto/create-profit.dto';
import { UserDashboardService } from '../user-dashboard/user-dashboard.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { CsvHistory } from './entities/csv-history.entity';
import { Broker } from '../broker/entity/broker';
import { Exchange } from '../exchanges/entities/exchange.entity';
const csv = require('csv-parser');
import { Readable } from 'stream';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class ProfitService {
  constructor(
    @InjectModel(Profit.name) private profitModel: Model<Profit>,
    @InjectModel(ReferralProfits.name)
    private referralProfitsModel: Model<ReferralProfits>,
    @InjectModel(CsvHistory.name)
    private csvHistoryModel: Model<CsvHistory>,
    @InjectModel(Broker.name)
    private brokerModel: Model<Broker>,
    @InjectModel(Exchange.name)
    private exchangeModel: Model<Exchange>,
    @InjectModel(ExchangeAccount.name)
    private exchangeAccountModel: Model<ExchangeAccount>,
    private dashboardService: DashboardService,
    @InjectModel(CreditDebitHistory.name)
    private creditDebitHistoryModel: Model<CreditDebitHistory>,
    @InjectModel(ManageAccount.name)
    private manageAccountModel: Model<ManageAccount>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
    @InjectModel(ReferralTree.name)
    private referralTreeModel: Model<ReferralTree>,
    private userDashboardService: UserDashboardService,
    private notificationsService: NotificationsService,
  ) {}

  async processCsvFile(
    csvBuffer: Buffer,
    brokerId: string,
  ): Promise<{ processed: number; errors: string[] }> {
    const results: CsvRowData[] = [];
    const errors: string[] = [];
    let processed = 0;

    return new Promise((resolve, reject) => {
      const stream = Readable.from(csvBuffer.toString());

      stream
        .pipe(
          csv({
            headers: [
              'tradingAccount',
              'name',
              'accountType',
              'nodeName',
              'platform',
              'rebateMethod',
              'ibCurrency',
              'rebate',
              'FOREX',
              'BULLION',
              'OIL',
              'CFD',
              'CRYPTO',
              'SHARES',
              'BTCUSD',
            ],
            skipEmptyLines: true,
            skipLinesWithError: true,
          }),
        )
        .on('data', (data) => {
          // Skip header row or invalid data
          if (
            data.tradingAccount === 'Trading Account' ||
            data.tradingAccount === 'tradingAccount' ||
            !data.tradingAccount ||
            data.tradingAccount.trim() === ''
          ) {
            console.log('Skipping header or invalid row:', data.tradingAccount);
            return;
          }

          // Convert rebate to number
          data.rebate = parseFloat(data.rebate) || 0;

          // Convert trading data to numbers
          [
            'FOREX',
            'BULLION',
            'OIL',
            'CFD',
            'CRYPTO',
            'SHARES',
            'BTCUSD',
          ].forEach((field) => {
            data[field] = parseFloat(data[field]) || 0;
          });

          console.log('Valid row added to results:', data.tradingAccount);
          results.push(data);
        })
        .on('end', async () => {
          try {
            console.log(`Processing ${results.length} valid rows from CSV`);
            for (let i = 0; i < results.length; i++) {
              const row = results[i];
              try {
                console.log(
                  `Processing row ${i + 1}/${results.length}: ${row.tradingAccount}`,
                );
                await this.processRow(row, brokerId);
                processed++;
                console.log(
                  `✅ Successfully processed row ${i + 1}: ${row.tradingAccount}`,
                );
              } catch (error) {
                console.log(
                  `❌ Error processing row ${i + 1}: ${row.tradingAccount} - ${error.message}`,
                );
                errors.push(`Account ${row.tradingAccount}: ${error.message}`);
              }
            }
            resolve({ processed, errors });
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          reject(
            new BadRequestException(`CSV parsing error: ${error.message}`),
          );
        });
    });
  }

  async processArrayData(
    data: CsvRowData[],
    brokerId?: string,
    startDate?: string,
    endDate?: string,
    exchangeId?: string,
  ): Promise<{ processed: number; errors: string[] }> {
    const errors: string[] = [];
    let processed = 0;
    let totalRebate = 0;

    // Validate that either brokerId or exchangeId is provided
    if (!brokerId && !exchangeId) {
      throw new BadRequestException('Either brokerId or exchangeId must be provided');
    }

    if (brokerId && exchangeId) {
      throw new BadRequestException('Cannot provide both brokerId and exchangeId');
    }

    console.log(`Processing ${data.length} records from array for ${brokerId ? 'broker' : 'exchange'}`);

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      try {
        console.log(
          `Processing row ${i + 1}/${data.length}: ${row.tradingAccount}`,
        );

        // Convert rebate to number
        row.rebate = parseFloat(row.rebate.toString()) || 0;

        // Add the rebate to the total
        totalRebate += parseFloat(row.rebate.toString()) || 0;

        await this.processRow(row, brokerId, exchangeId);
        processed++;
        console.log(
          `✅ Successfully processed row ${i + 1}: ${row.tradingAccount}`,
        );
      } catch (error) {
        console.log(
          `❌ Error processing row ${i + 1}: ${row.tradingAccount} - ${error.message}`,
        );
        errors.push(`Account ${row.tradingAccount}: ${error.message}`);
      }
    }

    // Update or create CsvHistory record and update total rebate
    if (brokerId && startDate && endDate) {
      await this.updateCsvHistory(brokerId, startDate, endDate, totalRebate);
      await this.updateBrokerTotalRebate(brokerId, totalRebate);
    } else if (exchangeId) {
      await this.updateExchangeTotalRebate(exchangeId, totalRebate);
    }

    return { processed, errors };
  }

  private async processRow(row: CsvRowData, brokerId?: string, exchangeId?: string): Promise<void> {
    // Skip if trading account is invalid
    if (!row.tradingAccount || row.tradingAccount.toString().trim() === '') {
      throw new Error('Invalid trading account number');
    }

    console.log(
      'Processing row for trading account:',
      row.tradingAccount,
      brokerId ? 'brokerId:' : 'exchangeId:',
      brokerId || exchangeId,
    );

    const accNo = row.tradingAccount.toString().trim();
    let account: any;

    if (brokerId) {
      // Look for broker account
      account = await this.manageAccountModel.findOne({
        accountNo: accNo,
        brokerId: brokerId,
      });
    } else if (exchangeId) {
      // Look for exchange account
      account = await this.exchangeAccountModel.findOne({
        accountNo: accNo,
        exchangeId: exchangeId,
      });
    }

    console.log(
      'Account lookup result:',
      account ? `Found account for ${accNo}` : `No account found for ${accNo}`,
    );

    if (!account) {
      const entityType = brokerId ? 'broker' : 'exchange';
      const entityId = brokerId || exchangeId;
      throw new Error(
        `Trading account ${row.tradingAccount} not found for ${entityType} ${entityId}`,
      );
    }

    const userId = account.userId as any;

    const user = await this.authModel.findById(userId);

    if (!user) {
      throw new Error(`User not found for account ${row.tradingAccount}`);
    }


    let broker: any = null;
    let exchange: any = null;
    let profitPercentage = 50; // default

    if (brokerId) {
      broker = await this.brokerModel.findById(brokerId);
      if (!broker) {
        throw new Error(`Broker not found`);
      }
      profitPercentage = broker.profitPercentage || 50;
    } else if (exchangeId) {
      exchange = await this.exchangeModel.findById(exchangeId);
      if (!exchange) {
        throw new Error(`Exchange not found`);
      }
      profitPercentage = exchange.profitPercentage || 50;
    }

    const totalRebate: number = row.rebate;
    const userProfit: number = totalRebate * (profitPercentage / 100);
    const platformProfit: number = totalRebate * ((100 - profitPercentage) / 100);

    console.log('profitPercentage:', profitPercentage);
    console.log('userProfit:', userProfit);
    console.log('platformProfit:', platformProfit);
    console.log('totalRebate:', totalRebate);

    // Calculate referral profits

    let referralLevel1 = 0;
    let referralLevel2 = 0;
    let referralLevel3 = 0;
    let level1UserId = null;
    let level2UserId = null;
    let level3UserId = null;

    // Get referral chain using helper function logic
    const referralChain = await this.getReferralChain(user);

    if (referralChain.length >= 1) {
      referralLevel1 = userProfit * 0.1; // 10% to direct referrer
      level1UserId = referralChain[0].userId;
    }

    if (referralChain.length >= 2) {
      referralLevel2 = userProfit * 0.05; // 5% to referrer's referrer
      level2UserId = referralChain[1].userId;
    }

    if (referralChain.length >= 3) {
      referralLevel3 = userProfit * 0.02; // 2% to referrer's referrer's referrer
      level3UserId = referralChain[2].userId;
    }
    console.log(
      `Referral chain for user ${user.email}:`,
      referralChain.length > 0
        ? `${referralChain.length} levels found`
        : 'No referrals',
    );

    // Update user's balance (remaining after referral deductions)
    const finalUserProfit = userProfit;
    const balanceBefore = user.balance;
    user.balance += finalUserProfit;
    const balanceAfter = user.balance;
    user.totalTradeEarnings = user.totalTradeEarnings + finalUserProfit;
    user.totalRebate += totalRebate;
    await this.dashboardService.addBrokerRebate(totalRebate);
    await this.userDashboardService.addTradeProfit(user._id.toString(), finalUserProfit);
    await this.userDashboardService.addRebate(user._id.toString(), totalRebate);
    await this.userDashboardService.updateUserBalance(user._id.toString(), user.balance);

    if(user.totalTradeEarnings >= 10000 && user.rank != "gold"){
      user.rank = 'gold';
    }
    else if(user.totalTradeEarnings >= 5000 && user.rank != "silver"){
      user.rank = 'silver';
    }

    await user.save();

    // Create rebate notification for the user
    try {
      await this.notificationsService.createRebateNotification(
        user._id.toString(),
        finalUserProfit
      );
      console.log(`✅ Rebate notification sent to user: ${user.email}`);
    } catch (error) {
      console.error(`❌ Failed to send rebate notification to ${user.email}:`, error);
    }

    // Create credit-debit history record for trade profit
    const tradeHistoryRecord = new this.creditDebitHistoryModel({
      userId: user._id,
      type: 'credit',
      amount: finalUserProfit,
      firstname: user.firstName,
      lastname: user.lastName,
      accountNo: row.tradingAccount,
      source: 'trade',
      balanceBefore: balanceBefore,
      balanceAfter: balanceAfter,
      from: "admin",
      to: user.firstName + " " + user.lastName,

    });
    await tradeHistoryRecord.save();

    // Update account total profit
    account.totalProfit += userProfit;
    await account.save();
    // }

    // Create profit record
    const profitRecord = new this.profitModel({
      userId: user._id,
      brokerId: brokerId || undefined,
      exchangeId: exchangeId || undefined,
      tradingAccount: row.tradingAccount,
      totalRebate,
      userProfit: finalUserProfit,
      platformProfit,
      csvData: row.csvData || [],
    });

    await profitRecord.save();

    // Update referrer profits and balances
    if (referralChain.length > 0) {
      await this.updateReferrerProfits(
        referralChain,
        {
          level1: { amount: referralLevel1, userId: level1UserId },
          level2: { amount: referralLevel2, userId: level2UserId },
          level3: { amount: referralLevel3, userId: level3UserId },
        },
        user._id.toString(),
        row.tradingAccount,
        (brokerId || exchangeId) as string,
      );
    }
  }

  private async getReferralChain(user: any): Promise<any[]> {
    const referralChain: any[] = [];
    let currentReferrer = user.refferedBy
      ? await this.authModel.findOne({ referralCode: user.refferedBy })
      : null;
    let level = 1;

    while (currentReferrer && level <= 3) {
      referralChain.push({
        level: level,
        userId: currentReferrer._id.toString(),
        userEmail: currentReferrer.email,
        userName: `${currentReferrer.firstName} ${currentReferrer.lastName}`,
        referralCode: currentReferrer.referralCode,
        referredBy: currentReferrer.refferedBy || null,
      });

      // Move to next referrer
      if (currentReferrer.refferedBy) {
        currentReferrer = await this.authModel.findOne({
          referralCode: currentReferrer.refferedBy,
        });
      } else {
        break;
      }
      level++;
    }

    return referralChain;
  }

  private async updateReferrerProfits(
    _referralChain: any[],
    amounts: {
      level1: { amount: number; userId: string | null };
      level2: { amount: number; userId: string | null };
      level3: { amount: number; userId: string | null };
    },
    referredUserId: string,
    tradingAccount: string,
    brokerId: string,
  ): Promise<void> {
    // Add profit to direct referrer (level 1)
    if (amounts.level1.userId && amounts.level1.amount > 0) {
      await this.addReferralProfit(
        amounts.level1.userId,
        amounts.level1.amount,
        1,
        referredUserId,
        tradingAccount,
        brokerId,
      );
    }

    // Add profit to level 2 referrer
    if (amounts.level2.userId && amounts.level2.amount > 0) {
      await this.addReferralProfit(
        amounts.level2.userId,
        amounts.level2.amount,
        2,
        referredUserId,
        tradingAccount,
        brokerId,
      );
    }

    // Add profit to level 3 referrer
    if (amounts.level3.userId && amounts.level3.amount > 0) {
      await this.addReferralProfit(
        amounts.level3.userId,
        amounts.level3.amount,
        3,
        referredUserId,
        tradingAccount,
        brokerId,
      );
    }
  }

  private async addReferralProfit(
    userId: string,
    amount: number,
    level: number,
    referredUserId: string,
    tradingAccount: string,
    brokerId: string,
  ): Promise<void> {
    // Update referrer's balance and totalReferralEarnings in auth entity
    const referrer = await this.authModel.findById(userId);
    if (referrer) {
      const balanceBefore = referrer.balance;
      referrer.balance += amount;
      const balanceAfter = referrer.balance;
      referrer.totalReferralEarnings += amount;
      if(level === 1){
        referrer.level1Earnings += amount;
      }
      else if(level === 2){
        referrer.level2Earnings += amount;
      }
      else if(level === 3){
        referrer.level3Earnings += amount;
      }
      
      await referrer.save();
      
      // Add this line to update user monthly dashboard
      await this.userDashboardService.addReferralEarning(userId, amount, level);
      await this.userDashboardService.updateUserBalance(userId, referrer.balance);
      
      // Create referral earning notification
      try {
        await this.notificationsService.createReferralNotification(
          userId,
          level,
          amount,
          referredUserId
        );
        console.log(`✅ Referral earning notification sent to user: ${referrer.email} for level ${level}`);
      } catch (error) {
        console.error(`❌ Failed to send referral notification to ${referrer.email}:`, error);
      }
      
      // Get referred user details for the from field
      const referredUser = await this.authModel.findById(referredUserId);
      const referredUserName = referredUser ? `${referredUser.firstName} ${referredUser.lastName}` : 'Unknown User';
      
      // Create credit-debit history record for referral earning
      const referralHistoryRecord = new this.creditDebitHistoryModel({
        userId: referrer._id,
        type: 'credit',
        amount: amount,
        firstname: referrer.firstName,
        lastname: referrer.lastName,
        accountNo: tradingAccount,
        source: 'referral',
        currency: 'usd',
        description: `Level ${level} referral earning from account ${tradingAccount} - $${amount.toFixed(2)}`,
        balanceBefore: balanceBefore,
        balanceAfter: balanceAfter,
        from: referredUserName,
        to: referrer.firstName + " " + referrer.lastName,
      });
      await referralHistoryRecord.save();
    }

    // Create a referral profit record in separate table
    const referralProfit = new this.referralProfitsModel({
      userId: userId,
      referredUserId: referredUserId,
      brokerId: brokerId,
      referralLevel: level,
      profitAmount: amount,
      tradingAccount: tradingAccount,
      description: `Level ${level} referral profit from trading account ${tradingAccount}. Amount: $${amount.toFixed(2)}`,
    });

    await referralProfit.save();
  }

  async getUserProfits(userId: string): Promise<Profit[]> {
    return await this.profitModel
      .find({ userId })
      .populate('brokerId', 'brokerName brokerType')
      .sort({ createdAt: -1 });
  }

  async getAllProfits(): Promise<Profit[]> {
    return await this.profitModel
      .find()
      .populate('userId', 'firstName lastName email')
      .populate('brokerId', 'brokerName brokerType')
      .sort({ createdAt: -1 });
  }

  async getProfitSummary(userId: string): Promise<any> {
    const profits = await this.profitModel.find({ userId });
    const referralProfits = await this.referralProfitsModel.find({ userId });

    const summary = {
      totalUserProfit: 0,
      totalReferralEarnings: 0,
      totalTradingProfit: 0,
      tradingAccounts: profits.length,
      referralBonuses: referralProfits.length,
    };

    // Calculate trading profits
    profits.forEach((profit) => {
      summary.totalTradingProfit += profit.userProfit;
    });

    // Calculate referral earnings
    referralProfits.forEach((referralProfit) => {
      summary.totalReferralEarnings += referralProfit.profitAmount;
    });

    summary.totalUserProfit =
      summary.totalTradingProfit + summary.totalReferralEarnings;

    return summary;
  }

  async getUserReferralProfits(userId: string): Promise<ReferralProfits[]> {
    return await this.referralProfitsModel
      .find({ userId })
      .populate('referredUserId', 'firstName lastName email')
      .sort({ createdAt: -1 });
  }

  async getAllReferralProfits(): Promise<ReferralProfits[]> {
    return await this.referralProfitsModel
      .find()
      .populate('userId', 'firstName lastName email')
      .populate('referredUserId', 'firstName lastName email')
      .sort({ createdAt: -1 });
  }

  // Credit-Debit History Methods
  async getUserCreditDebitHistory(userId: string): Promise<CreditDebitHistory[]> {
    return await this.creditDebitHistoryModel
      .find({ userId })
      .sort({ createdAt: -1 });
  }

  async getAllCreditDebitHistory(): Promise<CreditDebitHistory[]> {
    return await this.creditDebitHistoryModel
      .find()
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 });
  }

  async getCreditDebitHistoryBySource(source: string): Promise<CreditDebitHistory[]> {
    return await this.creditDebitHistoryModel
      .find({ source })
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 });
  }

  async getCreditDebitHistoryByUserId(targetUserId: string, adminId: string): Promise<CreditDebitHistory[]> {
    console.log('📋 Admin getting credit-debit history for user:', targetUserId);

    // Check if requester is admin
    const admin = await this.authModel.findById(adminId);
    if (!admin || admin.role !== 'admin') {
      throw new BadRequestException('Only admin can view user credit-debit history');
    }

    // Check if target user exists
    const targetUser = await this.authModel.findById(targetUserId);
    if (!targetUser) {
      throw new BadRequestException('Target user not found');
    }

    if (targetUser.role === 'admin') {
      throw new BadRequestException('Cannot view admin user credit-debit history');
    }

    // Get all credit-debit history for the target user
    const history = await this.creditDebitHistoryModel
      .find({ userId: targetUserId })
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 });

    console.log(`✅ Found ${history.length} credit-debit records for user ${targetUserId}`);
    return history;
  }

  // New method to update or create CsvHistory
  private async updateCsvHistory(
    brokerId: string, 
    startDate: string, 
    endDate: string, 
    totalRebate: number
  ): Promise<void> {
    try {
      // Find existing record for this broker and date range
      const existingRecord = await this.csvHistoryModel.findOne({
        brokerId,
        startDate: startDate,
        endDate: endDate
      });

      if (existingRecord) {
        // Replace existing record
        existingRecord.totalRebate = totalRebate;
        await existingRecord.save();
      } else {
        // Create new record
        const newRecord = new this.csvHistoryModel({
          brokerId,
          startDate: startDate,
          endDate: endDate,
          totalRebate
        });
        await newRecord.save();
      }
    } catch (error) {
      console.error('Error updating CSV history:', error);
      throw new Error(`Failed to update CSV history: ${error.message}`);
    }
  }

  // New method to update broker's total rebate
  private async updateBrokerTotalRebate(
    brokerId: string,
    additionalRebate: number
  ): Promise<void> {
    try {
      const broker = await this.brokerModel.findById(brokerId);
      if (broker) {
        broker.totalRebate = (broker.totalRebate || 0) + additionalRebate;
        await broker.save();
      }
    } catch (error) {
      console.error('Error updating broker total rebate:', error);
      throw new Error(`Failed to update broker total rebate: ${error.message}`);
    }
  }

  // New method to update exchange's total rebate
  private async updateExchangeTotalRebate(
    exchangeId: string,
    additionalRebate: number
  ): Promise<void> {
    try {
      const exchange = await this.exchangeModel.findById(exchangeId);
      if (exchange) {
        exchange.totalRebate = (exchange.totalRebate || 0) + additionalRebate;
        await exchange.save();
      }
    } catch (error) {
      console.error('Error updating exchange total rebate:', error);
      throw new Error(`Failed to update exchange total rebate: ${error.message}`);
    }
  }

  // Add this method to get CSV history for a specific broker
  async getCsvHistory(brokerId: string): Promise<CsvHistory[]> {
    try {
      const csvHistory = await this.csvHistoryModel
        .find({ brokerId })
        .sort({ createdAt: -1 });
      
      return csvHistory;
    } catch (error) {
      console.error('Error fetching CSV history:', error);
      throw new Error(`Failed to fetch CSV history: ${error.message}`);
    }
  }

  async verifyCsvData(
    data: CsvRowData[],
    brokerId?: string,
    exchangeId?: string,
  ): Promise<{ validAccounts: string[]; invalidAccounts: string[] }> {
    const validAccounts: string[] = [];
    const invalidAccounts: string[] = [];

    const entityType = brokerId ? 'broker' : 'exchange';
    const entityId = brokerId || exchangeId;
    console.log(`Verifying ${data.length} records for ${entityType} ${entityId}`);

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const accNo = row.tradingAccount?.toString().trim();

      if (!accNo || accNo === '') {
        invalidAccounts.push(`Row ${i + 1}: Empty trading account`);
        continue;
      }

      try {
        let account: any;

        if (brokerId) {
          account = await this.manageAccountModel.findOne({
            accountNo: accNo,
            brokerId: brokerId,
          });
        } else if (exchangeId) {
          account = await this.exchangeAccountModel.findOne({
            accountNo: accNo,
            exchangeId: exchangeId,
          });
        }

        if (account) {
          validAccounts.push(accNo);
        } else {
          invalidAccounts.push(accNo);
        }
      } catch (error) {
        invalidAccounts.push(`${accNo}: ${error.message}`);
      }
    }

    return { validAccounts, invalidAccounts };
  }
}
