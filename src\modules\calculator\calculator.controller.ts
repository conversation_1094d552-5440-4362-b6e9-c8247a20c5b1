import { Controller, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CalculatorService, CalculatorResult } from './calculator.service';
import { TradingCalculatorDto } from './dto/calculator.dto';
import { Public } from '../../Gaurd/public.decorator';

@ApiTags('Trading Calculator')
@Controller('calculator')
export class CalculatorController {
  constructor(private readonly calculatorService: CalculatorService) {}

  @Post('trading-profit')
  @Public()
  @ApiOperation({
    summary: 'Calculate trading profit',
    description: 'Calculate daily, monthly and yearly profit based on trading parameters'
  })
  @ApiResponse({
    status: 200,
    description: 'Profit calculation completed successfully',
    schema: {
      type: 'object',
      properties: {
        dailyProfit: { type: 'number', example: 25.50 },
        monthlyProfit: { type: 'number', example: 765.00 },
        yearlyProfit: { type: 'number', example: 9307.50 },
        input: {
          type: 'object',
          properties: {
            unit: { type: 'string', example: 'pips' },
            lots: { type: 'number', example: 1 },
            tradesPerDay: { type: 'number', example: 5 },
            rebate: { type: 'number', example: 0.50 }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid input parameters' })
  calculateTradingProfit(@Body() calculatorDto: TradingCalculatorDto): CalculatorResult {
    return this.calculatorService.calculateTradingProfit(calculatorDto);
  }
}
