import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class ReferralTree extends Document {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  userEmail: string;

  @Prop({ required: true })
  userName: string;

  @Prop({ type: Array, default: [] })
  referralChain: Array<{
    level: number;
    userId: string;
    userEmail: string;
    userName: string;
    referralCode: string;
    referredBy: string;
  }>;

  @Prop({ default: 0 })
  totalLevels: number;

  @Prop({ type: Object, default: null })
  rootReferrer: {
    userId: string;
    userEmail: string;
    userName: string;
    referralCode: string;
    level: number;
  };
}

export const ReferralTreeSchema = SchemaFactory.createForClass(ReferralTree);
