import { Storage } from '@google-cloud/storage';
import * as path from 'path';

// Initialize Google Cloud Storage
const storage = new Storage({
  keyFilename: path.join(process.cwd(), 'google-cloud-key.json'),
  projectId: 'legion-super-app',
});


const BUCKET_NAME = 'tradebucket1213'; 

const bucket = storage.bucket(BUCKET_NAME);

export interface UploadResult {
  fileName: string;
  publicUrl: string;
  gsUrl: string;
  signedUrl?: string;
}

/**
 * Upload file to Google Cloud Storage
 * @param file - File buffer or stream
 * @param fileName - Name for the file in storage
 * @param folder - Optional folder path (e.g., 'brokers', 'profiles')
 * @returns Promise with upload result
 */
export async function uploadToGCS(
  file: Buffer | Express.Multer.File,
  fileName: string,
  folder?: string
): Promise<UploadResult> {
  try {
    // Create full file path with folder
    const fullFileName = folder ? `${folder}/${fileName}` : fileName;
    
    // Get file buffer
    const fileBuffer = Buffer.isBuffer(file) ? file : file.buffer;
    
    // Create file reference in bucket
    const fileRef = bucket.file(fullFileName);
    
    // Upload file
    await fileRef.save(fileBuffer, {
      metadata: {
        contentType: getContentType(fileName),
      },
      // Remove public: true for uniform bucket-level access
    });

    // Try to make file publicly accessible
    try {
      await fileRef.makePublic();
      console.log('File made public successfully');
    } catch (publicError) {
      console.log('Could not make file public (bucket may have uniform access):', publicError.message);
      // File is still uploaded, just may not be publicly accessible via direct URL
    }
    
    // Generate URLs
    const publicUrl = `https://storage.googleapis.com/${BUCKET_NAME}/${fullFileName}`;
    const gsUrl = `gs://${BUCKET_NAME}/${fullFileName}`;

    // Generate signed URL for secure access (valid for 7 days)
    let signedUrl: string | undefined;
    try {
      const [url] = await fileRef.getSignedUrl({
        action: 'read',
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      });
      signedUrl = url;
      console.log(`File uploaded successfully with signed URL`);
    } catch (signedUrlError) {
      console.log('Could not generate signed URL:', signedUrlError.message);
    }

    console.log(`File uploaded successfully: ${publicUrl}`);

    return {
      fileName: fullFileName,
      publicUrl,
      gsUrl,
      signedUrl,
    };
  } catch (error) {
    console.error('Error uploading to GCS:', error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
}

/**
 * Delete file from Google Cloud Storage
 * @param fileName - Full file name/path in storage
 */
export async function deleteFromGCS(fileName: string): Promise<void> {
  try {
    await bucket.file(fileName).delete();
    console.log(`File deleted successfully: ${fileName}`);
  } catch (error) {
    console.error('Error deleting from GCS:', error);
    throw new Error(`Failed to delete file: ${error.message}`);
  }
}

/**
 * Get content type based on file extension
 * @param fileName - File name with extension
 * @returns Content type string
 */
function getContentType(fileName: string): string {
  const ext = path.extname(fileName).toLowerCase();
  
  const contentTypes: { [key: string]: string } = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.txt': 'text/plain',
    '.csv': 'text/csv',
  };
  
  return contentTypes[ext] || 'application/octet-stream';
}

/**
 * Generate unique file name with timestamp
 * @param originalName - Original file name
 * @returns Unique file name
 */
export function generateUniqueFileName(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const ext = path.extname(originalName);
  const nameWithoutExt = path.basename(originalName, ext);
  
  return `${nameWithoutExt}_${timestamp}_${randomString}${ext}`;
}
