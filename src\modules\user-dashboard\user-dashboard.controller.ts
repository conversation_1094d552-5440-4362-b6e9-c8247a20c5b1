import { <PERSON>, Get, Param, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { UserDashboardService } from './user-dashboard.service';
import { JwtAuthGuard } from '../../Gaurd/jwt-auth.guard';

@ApiTags('User Dashboard')
@Controller('user-dashboard')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
export class UserDashboardController {
  constructor(private readonly userDashboardService: UserDashboardService) {}

  @Get('rank')
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get user rank from token' })
  @UseGuards(JwtAuthGuard)
  async getUserRank(@Request() req: any) {
    const userId = req.user.userId;
    return this.userDashboardService.getUserRank(userId);
  }

  @Get('current')
  @ApiOperation({ summary: 'Get current month dashboard for authenticated user' })
  async getCurrentUserDashboard(@Request() req: any) {
    const userId = req.user.userId;
    return this.userDashboardService.getCurrentMonthDashboard(userId);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get dashboard history for authenticated user' })
  async getCurrentUserDashboardHistory(@Request() req: any) {
    const userId = req.user.userId;
    return this.userDashboardService.getUserMonthlyDashboard(userId);
  }

  @Get('earnings-history')
  @ApiOperation({ summary: 'Get user earnings history by date range' })
  @ApiQuery({ name: 'startDate', description: 'Start date (YYYY-MM-DD)', example: '2025-07-11' })
  @ApiQuery({ name: 'endDate', description: 'End date (YYYY-MM-DD)', example: '2025-07-21' })
  async getUserEarningsHistory(
    @Request() req: any,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const userId = req.user.userId;
    return this.userDashboardService.getUserEarningsByDateRange(userId, startDate, endDate);
  }

  @Get(':userId')
  @ApiOperation({ summary: 'Get current month dashboard by user ID (Admin only)' })
  async getUserDashboard(@Param('userId') userId: string) {
    return this.userDashboardService.getCurrentMonthDashboard(userId);
  }

  @Get(':userId/history')
  @ApiOperation({ summary: 'Get dashboard history by user ID (Admin only)' })
  async getUserDashboardHistory(@Param('userId') userId: string) {
    return this.userDashboardService.getUserMonthlyDashboard(userId);
  }
}
