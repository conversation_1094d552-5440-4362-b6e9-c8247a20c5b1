import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { LiveChatService } from './chat.service';
import { LiveChatController } from './chat.controller';
import { ChatRoom, ChatRoomSchema } from './entities/chat.entity';
import { Message, MessageSchema } from './entities/message.entity';
import { Auth, AuthSchema } from '../auth/entities/auth.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatRoom.name, schema: ChatRoomSchema },
      { name: Message.name, schema: MessageSchema },
      { name: Auth.name, schema: AuthSchema },
    ]),
    JwtModule.register({
      secret:
        process.env.JWT_SECRET ||
        'your-super-secret-jwt-key-change-in-production',
      signOptions: { expiresIn: '7d' },
    }),
  ],
  providers: [LiveChatService],
  controllers: [LiveChatController],
  exports: [LiveChatService],
})
export class ChatModule {}
